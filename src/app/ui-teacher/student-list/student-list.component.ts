// IMPORTS
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, SimpleChanges, OnChanges } from "@angular/core";
import { ListSelectService, ListSelector } from "../../ui-partial/list-select.service";
import { IStudentAccount } from "../../ui-schooladmin/data/types";
import { mtz } from "../../core/util/moment";
import { ClassroomsService } from "../../core/classrooms.service";
import { LoginGuardService } from "../../api/login-guard.service";
import { LangService } from "../../core/lang.service";
import { StudentG9ConnectionService } from "../../ui-student/student-g9-connection.service";
import * as moment from "moment-timezone";
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { AuthService } from "../../api/auth.service";
import { RoutesService } from "../../api/routes.service";
import { WhitelabelService } from "../../domain/whitelabel.service";
import { G9DemoDataService } from "../../ui-schooladmin/g9-demo-data.service";
import { OnlineOrPaperService } from "../../ui-testrunner/online-or-paper.service";
import { StudentSoftLockService } from "../../ui-student/student-soft-lock.service";
import { ASSESSION_SLUG_MAPPING, ASSESSMENT, IClassroom, IStudent, TeacherModal} from '../data/types';
import { ScanInfoService } from "../scan-info.service";
import { AssessmentCode } from "src/app/ui-schooladmin/data/mappings/eqao-g9";
import {
  TIME_REM_INTERVAL_MS,
  WL_SITE_FLAGS,
  StudentQuestionResponseInfoStatusType, PJSampleLangTitle, PJSampleMathTitle, PJOperationalLangTitle, PJOperationalMathTitle,
} from './model/constants'
import {
  ISubSessionRecord,
  IReviewSSModalConfig,
  IStudentState,
} from './model/types';
import { ITimeExtUpdate } from "../widget-time-ext/widget-time-ext.component";
import { IQuestionScan } from "../view-invigilate/types";
import { IGenRespSheetConfig } from '../scan-button-bar/scan-button-bar.component';
import { FLAGS } from '../../domain/whitelabel.service';

// EXPORTS
export {
  IStudentState
}

// COMPONENT
@Component({
  selector: "student-list",
  templateUrl: "./student-list.component.html",
  styleUrls: ["./student-list.component.scss"]
})
export class StudentListComponent implements OnInit, OnChanges, OnDestroy {
  @Input() isWalkinList: boolean = false;
  @Input() studentList: IStudentAccount[];
  @Input() closeAllSubSessions: boolean;
  @Input() isSessionOpened: boolean;
  @Input() isDurationEnforced:boolean;
  @Input() duration_m:number;
  @Input() time_ext_m:number;
  @Input() isShowTimeRemaining: boolean;
  @Output() selectionUpdate = new EventEmitter();
  @Output() updateStudentSoftLock = new EventEmitter();
  isDismissedSessionANotfifcation: boolean = false;
  isDismissedSessionBNotification: boolean = false;
  isDismissedSessionBNotificationWarning: boolean = false;
  isDismissedSessionANotfifcationWarning: boolean = false;
  @Output() isAllSubSessionsClosed = new EventEmitter();
  @Output() resetSoftLock = new EventEmitter();
  @Output() acceptAllWalkStudents = new EventEmitter();
  @Output() rejectAllWalkStudents = new EventEmitter();
  @Output() acceptWalkStudent = new EventEmitter();
  @Output() rejectWalkStudent = new EventEmitter();
  @Output() openStudentInfo = new EventEmitter();
  @Output() questionId = new EventEmitter();
  @Output() openEditModal = new EventEmitter();
  @Output() openSoftlockModal = new EventEmitter();
  @Output() openUnpauseModal = new EventEmitter();
  /////////////////
  @Input() isManagingStudents: boolean;
  /////////////////
  @Input() testSessionId: number;
  @Input() classId: number;
  @Input() activeSession;
  @Input() subSessions: ISubSessionRecord[] = [];
  @Input() activeSubSessions;
  @Input() activeClassroom: IClassroom;
  @Input() completedSubSessions;
  @Input() studentStates: { [key: number]: IStudentState };
  @Input() studentSocketState: { [key: number]: any };
  @Input() isNoTdOrder: boolean;
  @Output() locksUpdate = new EventEmitter();
  @Output() reloadSessionInfo: EventEmitter<any> = new EventEmitter();
  @Input() isUnsubmitBtnVisiable: boolean
  @Output() isUnsubmitBtnVisiableChange = new EventEmitter<boolean>();
  @Input() asmtSlug;
  @Input() isSecreteUser;
  @Input() testAttemptSoftLockStatus;
  @Output() studentLockActionEmitter: EventEmitter<any> = new EventEmitter();
  @Input() isForceUnsubmitAbility: boolean;
  @Input() isSasnLogin;
  @Output() disableSoftLock = new EventEmitter();
  @Output() notifyUserSoftLock = new EventEmitter();
  @Input() isUserSoftLockNotified: boolean;
  @Input() isScanSession: boolean;
  @Input() isStudentDetailAccess: boolean = true;
  @Input() printUploadStatus: { isPrintDisabled: boolean, isUploadDisabled: boolean } = { isPrintDisabled: true, isUploadDisabled: true };
  @Input() questionsForScan: IQuestionScan[];
  @Input() studentScanInfo;
  studentsToReviewSS: IStudentAccount[];
  studentSSSubmissionMap: any;
  subsessionIndToReview: number;
  showOnlyRegisteredStudents: boolean = false;
  ssReviewForAll: boolean = false;
  isSoftLockReset: boolean = false;
  isSessionAlocked = true;
  // isSessionBlocked = false;
  isOnlineStatusShow = false;
  showSessionAEdit = false;
  showSessionBEdit = false;
  studentLockState = new Map();
  studentLockNotification = new Map(); // for warning icon
  studentPauseNotification = new Map(); // for paused icon
  studentSubSessionToRestore = new Map();
  studentIsLockingUnlockingStatus = new Map(); // for enabling/disabling lock button 
  overallSubSessionLockState = new Map();
  // studentComputedStates:{[key:number]:IStudentState};
  studentSelections: ListSelector;
  ticker: number;
  pageModal: PageModalController;
  currentStudent: IStudentAccount;
  assessment: ASSESSMENT;
  isWritingPaperSubmitted: boolean = false;
  unsubmitedStudent: IStudentAccount = null;
  unsubmitedSubSessionIndex: number = null;
  studentsToDisplay: IStudentAccount[] = [];
  isShowAllStudentIds = false;
  isSortByLockStatus: String = "";
  sortedStudentByLockStatusList: IStudentAccount[] = [];

  selectedSortOption: string = "asnAsc"; // Default sort option
  sortedStudentList: IStudentAccount[] = [];
  sortOptions = [
    { key: "asnAsc", label: this.lang.tra('asn_asc'), },
    { key: "asnDesc", label: this.lang.tra('asn_desc') },
    { key: "nameAsc", label: this.lang.tra('last_name_asc') },
    { key: "nameDesc", label: this.lang.tra('last_name_desc') },
    { key: "lockAsc", label: this.lang.tra('lock_asc') },
    { key: "lockDesc", label: this.lang.tra('lock_desc') }
  ];
  
  checkedStudentSubmissions: { [studentId: string]: boolean } = {};

  IS_OFFLINE_INDICATOR_ENABLED = true
  isTaSoftLockEnabled = false // This flag enables softlock based on Test_attempts table

  tooltipOptions = {
    placement:"right",
    theme:"light", 
    animationDuration:200,
    offset: 12
  }
  allowUnsubmit = new Set([ASSESSMENT.OSSLT_OPERATIONAL, ASSESSMENT.G9_OPERATIONAL, ASSESSMENT.TCLE_OPERATIONAL, ASSESSMENT.TCN_OPERATIONAL, ASSESSMENT.SCIENCES8_OPERATIONAL, ASSESSMENT.PRIMARY_OPERATIONAL, ASSESSMENT.JUNIOR_OPERATIONAL, ASSESSMENT.ABED_OPERATIONAL, ASSESSMENT.ABED_PWR])

  TeacherModal = TeacherModal;

  constructor(
    private listSelectService: ListSelectService,
    private loginGuard: LoginGuardService, 
    private pageModalService: PageModalService, 
    public lang: LangService, 
    private classroomsService: ClassroomsService, 
    private studentG9Connection: StudentG9ConnectionService, 
    private auth: AuthService, 
    private routes: RoutesService,
    private whiteLabelService: WhitelabelService,
    private g9DemoData: G9DemoDataService,
    private onlineOrPaper: OnlineOrPaperService,
    private studentSoftLock: StudentSoftLockService,
    private scanInfo: ScanInfoService,
  ){}

  ngOnInit(): void {
    this.studentsToDisplay = this.studentList;
    // this.studentSelections = this.listSelectService.defineNewSelectableList(this.studentList);
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.selectionUpdate.emit(this.studentSelections);
    this.checkAllStudentSessionState();
    this.updateOverallLockStates();
    this.refreshComputedState();
    this.initTimeRemainingInterval()
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.time_ext_m) {
      this.refreshComputedState();
    }
    if (changes.studentStates) {
      this.refreshComputedState();
    }
    if (changes.closeAllSubSessions && changes.closeAllSubSessions.currentValue) {
      this.lockAllSubSessions();
    }
    if (changes.studentList){
      this.studentsToDisplay = changes.studentList.currentValue;
      this.sortedStudentList = this.sortStudents();
    }
  }

  toggleShowAllStudentIds(){
    this.isShowAllStudentIds = !this.isShowAllStudentIds;
    const details = this.studentList.map(s => {
      return [
        this.getSecretId(s), 
        s[this.g9DemoData.getPropName('DateofBirth', '')], // todo:CONSISTENCY

      ].join('\t')
    })
    console.log(details.join('\n'))
  }

  ngOnDestroy(): void {
    clearInterval(this.intervalTimeRemaining)
  }

  intervalTimeRemaining:NodeJS.Timeout;
  initTimeRemainingInterval(){
    this.intervalTimeRemaining = setInterval(() => this.recomputeTimeRemaining(), TIME_REM_INTERVAL_MS)
  }
  recomputeTimeRemaining(){
    // todo: for now, using refreshComputedState
    // todo:ASSUMPTION computing allowed time with assumption of a single attempt in the test session (defaulting to first)
    // for (let i=0; i<this.studentList.length; i++){
    //   const student = this.studentList[i];
    // }
  }

  async removeStudentFromClass(student:IStudentAccount){
    const msg =  this.lang.tra('sa_remove_from_classroom')+'?'
    if (confirm(msg)){
      const school_class_group_id = this.activeClassroom.group_id
      if (student.uid && school_class_group_id){
        await this.auth.apiRemove(
          this.routes.EDUCATOR_STUDENTS, 
          this.activeClassroom.group_id, {
            query: { studentUid: student.uid, school_class_group_id}
          }
        )
        for (let i=0; i<this.studentList.length; i++){
          if (this.studentList[i].uid == student.uid){
            this.studentList.splice(i, 1)
          }
        }
        this.filterStudentsToDisplay()
      }
    }
  }

  refreshComputedState = () => {
    if (!this.subSessions || !this.studentStates) {
      return;
    }
    console.log('refreshComputedState')
    const nowTime = mtz().valueOf();
    let isAnyUnlocked: { [subSessionIndex: number]: boolean } = {};

    for (const [uid, studentState] of Object.entries(this.studentStates)) {
      console.log('refreshComputedState - Processing student uid', uid);
      // Calculate test attempt level time remaining (like API does)
      this.calculateTestAttemptTimeRemaining(studentState, nowTime);

      // Calculate per-subsession time spent (for subsession indicators)
      studentState.subSessions.forEach((studentSubSessionState, subSessionIndex: number) => {
        const isLocked = this.isStudentSessionLocked(studentState, subSessionIndex);
        if (!isLocked) {
          isAnyUnlocked[subSessionIndex] = true;
        }

        if (studentSubSessionState && studentSubSessionState.started_on) {
          let startTime = mtz(studentSubSessionState.started_on).valueOf();
          let endTime;
          if( !this.isDurationEnforced){
            if (studentSubSessionState.last_touch_on) {
              const touchTime = mtz(studentSubSessionState.last_touch_on).valueOf();
              endTime = touchTime;
            }
            else {
              endTime = startTime
            }
          }
          else{
            // todo: subtract pause time
            if (studentSubSessionState.last_locked_on && isLocked) {
              const lockTime = mtz(studentSubSessionState.last_locked_on).valueOf();
              if (studentSubSessionState.last_touch_on) {
                const touchTime = mtz(studentSubSessionState.last_touch_on).valueOf();
                endTime = Math.min(lockTime, touchTime);
              }
              else {
                endTime = lockTime;
              }
            }
            else {
              endTime = nowTime;
            }
          }
          const ms = endTime - startTime;
          const minutes = Math.max(Math.floor(ms / (1000 * 60)), 0);
          studentSubSessionState._timeSpent = minutes + " minute(s)";
        }

        // Keep legacy per-subsession time remaining for backward compatibility
        const indiv_time_ext_m = +(studentState.time_ext_m || 0);
        const legacyTimeRemaining = this.duration_m + this.time_ext_m + indiv_time_ext_m;
        studentSubSessionState._timeRemaining = legacyTimeRemaining;
        studentSubSessionState._time_ext_m = studentState.time_ext_m;
      });
    }

    this.subSessions.forEach((subSession, subSessionIndex) => {
      this.overallSubSessionLockState.set(subSessionIndex, !isAnyUnlocked[subSessionIndex]);
    });
  }

  private calculateTestAttemptTimeRemaining(studentState: any, nowTime: number) {
    console.log('calculateTestAttemptTimeRemaining - studentState.uid', studentState.uid);
    console.log('calculateTestAttemptTimeRemaining - studentState.ends_on', studentState.ends_on);
    console.log('calculateTestAttemptTimeRemaining - nowTime', nowTime);

    // Use API's ends_on calculation (simple and accurate)
    if (!studentState.ends_on) {
      console.log('calculateTestAttemptTimeRemaining - No ends_on from API, setting timeRemaining to 0');
      studentState._testAttemptTimeRemaining = 0;
      return;
    }

    // Calculate time remaining using API's ends_on (like student view does)
    const endTime = new Date(studentState.ends_on).getTime();
    const timeRemainingMs = endTime - nowTime;
    const timeRemaining = Math.max(0, Math.floor(timeRemainingMs / (1000 * 60)));

    console.log('calculateTestAttemptTimeRemaining - endTime from API', new Date(studentState.ends_on).toISOString());
    console.log('calculateTestAttemptTimeRemaining - timeRemainingMs', timeRemainingMs);
    console.log('calculateTestAttemptTimeRemaining - timeRemaining (minutes)', timeRemaining);

    // Store the calculated time remaining at student level
    studentState._testAttemptTimeRemaining = timeRemaining;
    console.log('calculateTestAttemptTimeRemaining - FINAL _testAttemptTimeRemaining', studentState._testAttemptTimeRemaining);
  }

  showStudentDetails(studentIdx: number, question_id: number = null) {
    if (!this.isStudentDetailAccess) return;
    this.questionId.emit(question_id);
    this.openStudentInfo.emit(studentIdx);
    // alert('Student info (including accessiblity info) will be available here, but is currently not enabled.')
  }

  getStudentIndexFromOriginalList(student:IStudentAccount){
    for(let i = 0; i < this.studentList.length; i++){
      if(this.studentList[i].id == student.id){
        return i;
      }
    }
  }

  updateOverallLockStates() {
    // this.subSessions.forEach((subSession, subSessionIndex) => {
    //   this.overallSubSessionLockState.set(subSessionIndex, true); // to do: should be based on summary of students
    // })
  }

  getOverallSubSessionLockState(subSessionIndex: number) {
    return this.overallSubSessionLockState.get(subSessionIndex);
  }

  getStudentUids() {
    return this.studentList.map(student => student.id);
  }

  disableOverallButton(subSessionIndex: number) {
    if (this.subSessions && this.subSessions[subSessionIndex].date_time_start) {
      if (this.verifyPastDate(this.subSessions[subSessionIndex].date_time_start)) {
        return false;
      }
    }
    return true;
  }

  isAllStudentOnline() {
    return this.studentList.every(student => {
      if (!this.studentSocketState[student.id]) {
        return false;
      }
      return true;
    });
  }

  anyStudentInDiffSession(subSessionIndex: number) {
    const sub_session_id = this.subSessions[subSessionIndex].id;
    for(const student of this.studentList) {
      const studentState = this.getStudentState(student);
      if(studentState.active_sub_session_id != null && studentState.active_sub_session_id != sub_session_id)  {
        return true;
      }      
    }
    return false;
  }

  isSessionActive(){
    const isSessionActive = this.isDateTimeInThePast(this.activeSession.date_time_start);
    if(!isSessionActive){
      this.loginGuard.disabledPopup(this.lang.tra("abed_future_unlock_error"));
    }
    return isSessionActive;
  }

  toggleOverallSubSessionLockForAll(subSessionIndex: number) {
    if(this.g9DemoData.getIsUnsubmitting()) return //disable the button when unsubmitting
    if (this.activeClassroom.openAssessments.length > 1) {
      this.loginGuard.confirmationReqActivate({
        caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_another_active_session_pj") : this.lang.tra("msg_another_active_session")
      });
    } else {
      if(this.isABED()){
        if(!this.isSessionActive()){
          return;
        }
      }
      const isClosing = !this.getOverallSubSessionLockState(subSessionIndex);

      let warnings = [];
      if (!isClosing) {
        // if(!this.isAllStudentOnline()) {
        //   warnings.push('unlock_warn_offline')
        // }
        // if(this.anyStudentInDiffSession(subSessionIndex)) {
        //   if(this.isG9OrOsslt()) {
        //     warnings.push('unlock_warn_mid_session')
        //   }
        //   if(this.isPrimaryOrJunior()) {
        //     warnings.push('unlock_warn_mid_session_pj')
        //   }
        // }
        // let warningProps = {};
        // for(let i = 0; i < warnings.length; i++) {
        //   warningProps[`WARNING_${i+1}`] = this.lang.tra(warnings[i]);
        // }

        const retrieveStudentSubsessionsToUnlock = () => {
          const studentsToUnlock = [];
          let requireSSReview = false;
          this.studentList.forEach(stu => {
            // const studentSubSessionState = this.getStudentSubSessionState(stu, subSessionIndex);
            const prevStudentSubSessionState = subSessionIndex > 0 ? this.getStudentSubSessionState(stu, subSessionIndex - 1) : this.getStudentSubSessionState(stu, subSessionIndex);
            const currStudentSubSessionState = this.getStudentSubSessionState(stu, subSessionIndex);
            const isUpcoming = this.isStudentSubSessionUpcoming(stu, subSessionIndex);
            // const isSurpassed = this.isStudentSubSessionSurpassed(stu, subSessionIndex);
            if ( prevStudentSubSessionState && !isUpcoming && !prevStudentSubSessionState.is_submitted) {
              requireSSReview = true;
              studentsToUnlock.push(stu);
            } else if (!isUpcoming && !currStudentSubSessionState.is_submitted) {
              studentsToUnlock.push(stu);
            }
          })
          return {studentsToUnlock, requireSSReview};
        }

        // if(warnings.length) {
        //   const studentsToUnlock = retrieveStudentSubsessionsToUnlock();
        //   this.loginGuard.confirmationReqActivate({
        //     caption: this.isG9OrOsslt() ? `msg_unlock_warning_${warnings.length}` : `msg_unlock_warning_${warnings.length + 2}`,
        //     props: warningProps,
        //     confirm: () => {
        //       if (studentsToUnlock.length > 0 && subSessionIndex != 0 && subSessionIndex != 4) {
        //         this.ssReviewForAll = true;
        //         this.reviewSubsessionProgressModalStart(studentsToUnlock, subSessionIndex, isClosing, true);
        //       } else {
        //         this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
        //       }
        //     }
        //   });
        // } else {
        const {studentsToUnlock, requireSSReview} = retrieveStudentSubsessionsToUnlock();
        let pjUnlockConditions;
        if (this.isPJOperationalTest()) {
          pjUnlockConditions = subSessionIndex != 4
        } else if (this.isPJSampleTest()) {
          pjUnlockConditions = subSessionIndex != 2
        }
        if (studentsToUnlock.length > 0 && subSessionIndex != 0 && pjUnlockConditions && requireSSReview && ((this.isPrimaryOrJunior() && subSessionIndex != 8) || (this.isG9OrOsslt() && subSessionIndex != 3))) {
          this.ssReviewForAll = true;
          this.reviewSubsessionProgressModalStart(studentsToUnlock, subSessionIndex, isClosing, true);
        } else {
          this.setStudentsLocks(isClosing, studentsToUnlock, subSessionIndex);
        }
        if (!isClosing){
          this.showUnlockWarning();
        }
          // this.setStudentsLocks(isClosing, studentsToUnlock, subSessionIndex);
        // }
        return;
      } else {
        this.loginGuard.confirmationReqActivate({
          caption: this.isG9OrOsslt() ? 'msg_lock_warning' : 'msg_lock_warning_pj',
          confirm: () => {
            this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
          }
        });
      }
    }
    //this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
  }

  enforceOverallSubSessionLockForAll(subSessionIndex: number) {
    const isClosing = true;
    this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
  }

  lockAllSubSessions() {
    Promise.all(
      this.subSessions.map(async (subSession, idx) => {
        this.enforceOverallSubSessionLockForAll(idx);
      })
    ).then(res => this.isAllSubSessionsClosed.emit(true));
  }

  setStudentsLocks(isClosing: boolean, studentList: IStudentAccount[], subSessionIndex: number, isSubmitting = false) {
    const students = [];
    const effectiveClosers = [];
    if (isClosing) {
      studentList.forEach(student => {
        if (!this.getStudentSessionLockState(student, subSessionIndex)) {
          students.push(student);
        }
      });
    } else {
      studentList.forEach(student => {
        const isLocked = this.getStudentSessionLockState(student, subSessionIndex);
        const isSurpassed = this.isStudentSubSessionSurpassed(student, subSessionIndex);
        const isUpcoming = this.isStudentSubSessionUpcoming(student, subSessionIndex);
        if (isLocked && !isUpcoming) {
        // if (isLocked && !isSurpassed && !isUpcoming) {
          students.push(student);
          if (this.isStudentSubSessionEffectiveClosing(student, subSessionIndex)) {
            effectiveClosers.push(student);
          }
        }
      });
    }
    if (students.length === 0) {
      return;
    }
    // else if (effectiveClosers.length > 0){
    //   const subSession = this.subSessions[subSessionIndex];
    //   this.loginGuard.confirmationReqActivate({
    //     caption: this.lang.tra('msg_cnf_effective_close', null, {NUM_STUDENTS: effectiveClosers.length}),
    //     btnCancelCaption: this.lang.tra('lbl_cnf_effective_close_cancel'),
    //     btnProceedCaption: this.lang.tra('lbl_cnf_effective_close_proceed', null, {SESSION_CAPTION: subSession.caption}),
    //     confirm: () => this.patchStudentLocks(subSessionIndex, isClosing, students)
    //   })
    // }
    // else{
    //   this.patchStudentLocks(subSessionIndex, isClosing, students);
    // }
    this.patchStudentLocks(subSessionIndex, isClosing, students, isSubmitting);
  }

  private patchStudentLocks(subSessionIndex: number, isClosing: boolean, students: IStudentAccount[], isSubmitting: boolean) {
    const studentUids = students.map(s => s.uid);
    const subSession = this.subSessions[subSessionIndex];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return this.classroomsService
      .invigOpenCloseSubSessionForStudents(this.testSessionId, subSession.id, subSession.twtdar_order, studentUids, this.classId, isClosing, params, this.isPrimaryOrJunior(),isSubmitting)
      .then(res => {
        if (isClosing) {
          students.forEach(student => {
            const studentState = this.getStudentState(student);
            studentState.active_sub_session_id = null;
          });
        } else {
          if(res.errors){
            //look for the flag that says certain/all students have been given the same permission for the same operational assessment under the same test window
            const locateExistingPermError = res.errors.find(e => e.message == "AlreadyHasPermission");
            if(locateExistingPermError){
              const relevantStudents = locateExistingPermError.students.map(s => {
                const locatedStudent = this.studentList.find(es => es.uid == s.uid);
                const name = locatedStudent.first_name + (locatedStudent.middle_name ? ` ${locatedStudent.middle_name} ` : " ") + locatedStudent.last_name;
                return `${name} — ${s.className}`
              });
              this.loginGuard.quickPopup(this.lang.tra("invig_existing_permission", null, {students: relevantStudents.join(`<br>`)}));
              //remove the students that the invigilator is not allowed to unlock so that they do not get processed locally as unlocked.
              students = students.filter(s => locateExistingPermError.students.findIndex(l_s => l_s.uid == s.uid) == -1);
            }
          }
          const targetSubSessionId = this.subSessions[subSessionIndex].id;
          students.forEach(student => {
            const studentState = this.getStudentState(student);
            if (studentState) {
              studentState.active_sub_session_id = targetSubSessionId;
            }
            this.studentLockState.set(student.uid, false);
          });
        }
        this.checkAllStudentSessionState();
        this.refreshComputedState();
        this.reloadSessionInfo.emit(null);
        this.studentLockActionEmitter.emit({studentLockActionMap: this.studentIsLockingUnlockingStatus, studentUids: studentUids});

        this.studentG9Connection.notifyStudentsOfSubsession(studentUids, !isClosing, subSession.caption, this.activeSession.slug, subSession.slug);
      })
      .catch(err => {
        if (err.message === "OTHER_SESSION_ACTIVE") {
          this.loginGuard.disabledPopup("Sub-session can't be unlocked because student currently has another active sub-session");
        }
        if (err.message === "COMPLETED_SUB_SESSION") {
          this.loginGuard.disabledPopup("Student has already made a submission for this sub-session");
        }
      });
  }

  getStudentLockingUnlockingStatus(student) {
    if (!this.studentIsLockingUnlockingStatus.has(student.uid)) return false;
    return this.studentIsLockingUnlockingStatus.get(student.uid);
  }

  lockSession(subSession) {
    this.isSessionAlocked = !this.isSessionAlocked;
    this.studentList.forEach(student => {
      student.session_a.is_session_active = !this.isSessionAlocked;
    });
    this.checkAllStudentSessionState();
  }

  showUnlockWarning(){
    if (this.isPrimaryOrJunior()){
      this.loginGuard.quickPopup(this.lang.tra('lbl_unlock_popupwarn') )
    }
  }

  toggleStudentSubSessionLock(student: IStudentAccount, subSessionIndex: number) {
    // console.log('toggleStudentSubSessionLock', student)
    console.log("toggle")
    if(this.g9DemoData.getIsUnsubmitting()) return //disable the button when unsubmitting
    this.studentIsLockingUnlockingStatus.set(student.uid, true);
    if (this.activeClassroom.openAssessments.length > 1) {
      this.loginGuard.confirmationReqActivate({
        caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_another_active_session_pj") : this.lang.tra("msg_another_active_session")
      });
    } else {
      if(this.isABED()){
        if(!this.isSessionActive()){
          return;
        }
      }
      const isLocked = this.getStudentSessionLockState(student, subSessionIndex);
      if (!this.isStudentSubSessionSubmitted(student, subSessionIndex) && !this.isStudentSubSessionSurpassed(student, subSessionIndex) && !isLocked) {
        this.loginGuard.confirmationReqActivate({
          caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_close_sub_session_pj") :  this.whiteLabelService.isABED() ? this.lang.tra("abed_close_session") : this.lang.tra("msg_close_sub_session"),
          confirm: () => {
            const targetSubSessionId = this.subSessions[subSessionIndex].id;
            const studentState = this.getStudentState(student);
            let isClosing = studentState.active_sub_session_id === targetSubSessionId;
            this.setStudentsLocks(isClosing, [student], subSessionIndex);
          },
          close: () => {
            this.studentIsLockingUnlockingStatus.set(student.uid, false);
          }
        });
      } 
      else if (this.isStudentSubSessionSubmitted(student, subSessionIndex))  {
        this.studentIsLockingUnlockingStatus.set(student.uid, false);
        return;
      } 
      else if (!this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
        const targetSubSessionId = this.subSessions[subSessionIndex].id;
        const studentState = this.getStudentState(student);
        const subSession = studentState.subSessions[subSessionIndex];
        let isClosing = studentState.active_sub_session_id === targetSubSessionId;

        const prevSubSession = subSessionIndex > 0 ? studentState.subSessions[subSessionIndex - 1] : null;

        const setLocks = () => {
          this.setStudentsLocks(isClosing, [student], subSessionIndex);
        }
        if(this.isG9OrOsslt() && !isClosing && studentState.active_sub_session_id != null) {
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra("invig_mid_session"),
            confirm: setLocks
          });
        } else if (prevSubSession) {
          if (!isClosing && subSession.started_on == null && prevSubSession.started_on && !prevSubSession.is_submitted && subSession.subsession_slug != 'session_q') {
            this.ssReviewForAll = false;
            this.reviewSubsessionProgressModalStart([student], subSessionIndex, isClosing, false);
          } else { 
            // opening new subsession for first time
            setLocks();
          }
        } else {
          setLocks();
        }
        if (!isClosing){
          this.showUnlockWarning()
        }
      } 
      else {
        if(this.isPrimaryOrJunior()) {
          this.loginGuard.disabledPopup(this.lang.tra("pj_previously_submitted"));
        }
        if(this.isG9OrOsslt()) {
          this.loginGuard.disabledPopup("Student has already submitted this sub-session in a another test session");
        }
      }
      // temp //////////
      // studentState.active_sub_session_id = newSubSessionId;
      ///////////
      // session.is_session_active = !session.is_session_active;
      // this.checkAllStudentSessionState();
      // this.refreshComputedState()
    }
  }

  initStudentSSSubmissionMap(ssMap: any) {
    this.studentSSSubmissionMap = ssMap;
  }

  reviewSubsessionProgressModalStart(students: IStudentAccount[], subSessionIndex: number, isClosing: boolean, isAll: boolean) {
    this.studentsToReviewSS = [];
    students.forEach(student => {
      this.studentsToReviewSS.push(student);
    })
    this.subsessionIndToReview = subSessionIndex - 1;
    const config: IReviewSSModalConfig = { isClosing: !isClosing, studentStates: this.studentStates, studentList: this.studentsToReviewSS, subSessionIndex: this.subsessionIndToReview, isAll };
    this.pageModal.newModal({
      type: TeacherModal.REVIEW_CURR_SS_STATUS_MODAL,
      config,
      finish: this.proceedSSSubmissions.bind(this),
      cancel: this.cancelReviewProgressModal.bind(this)
    })
  }

  cancelReviewProgressModal(config: IReviewSSModalConfig) {
    const {studentList} = config;
    studentList.forEach(student => {
      this.studentIsLockingUnlockingStatus.set(student.uid, false);
    })
  }

  proceedSSSubmissions(config:IReviewSSModalConfig) {
    const {isClosing, subSessionIndex, studentList} = config;
    const studentsUidsToUpdate = [];
    const studentsToUpdate = [];

    for (const [uid, stuData] of this.studentSSSubmissionMap.entries()) {
      if (stuData) {
        const student = studentList.find(stu => stu.uid == uid);
        studentsUidsToUpdate.push(student.uid);
        studentsToUpdate.push(student);
      }
    }

    studentsToUpdate.forEach(stu => {
      this.studentIsLockingUnlockingStatus.set(stu.uid, true);
    })
    const subSession = this.subSessions[subSessionIndex];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);

    if (studentsUidsToUpdate.length < 1) {
      this.pageModal.closeModal();
      return;
    }
    this.classroomsService
    .invigOpenCloseSubSessionForStudents(this.testSessionId, subSession.id, subSession.twtdar_order, studentsUidsToUpdate, this.classId, isClosing, params,this.isPrimaryOrJunior(), true)
      .then(res => {
        studentsToUpdate.forEach(student => {
          const studentState = this.getStudentState(student);
          studentState.active_sub_session_id = null;
          const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
          subSessionState.is_submitted = 1;
        });
        this.checkAllStudentSessionState();
        this.refreshComputedState();
        this.reloadSessionInfo.emit(null);
        this.studentLockActionEmitter.emit({studentLockActionMap: this.studentIsLockingUnlockingStatus, studentUids: studentsUidsToUpdate});

        this.studentG9Connection.notifyStudentsOfSubsession(studentsUidsToUpdate, !isClosing, subSession.caption, this.activeSession.slug, subSession.slug, true);

        this.setStudentsLocks(!isClosing, studentsToUpdate, subSessionIndex + 1, false); 
      })

    this.pageModal.closeModal();
  }

  checkAllStudentSessionState() {
    let num = 0;
    this.studentList.forEach(student => {
      for (let i = 0; i < this.subSessions.length; i++) {
        if (!this.getStudentSessionLockState(student, i)) {
          num++;
          break;
        }
      }
    });
    // this.isSessionAlocked = (num === 0)
    this.locksUpdate.emit({ num });
  }

  getStudentState(student: IStudentAccount) {
    if (this.studentStates) {
      return this.studentStates[student.uid];
    }
  }

  getStudentSocketState(student: IStudentAccount) {
    return this.studentSocketState[student.uid];
  }

  getStudentSubSessionState(student: IStudentAccount, subSessionIndex: number) {
    const state = this.getStudentState(student);
    if(!state) {
      return;
    }
    try{
      return state.subSessions[subSessionIndex];
    }catch(e){
      return;
    }
  }

  isStudentOnline(student: IStudentAccount) {
    //console.log(this.studentSocketState)
    return !!this.studentSocketState[student.uid];
  }
  isStudentClosing = false;
  pauseStudentAttempt(testAttemptIds, isPausing, student) {
    //console.log(subSessionIndex)
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return Promise.all(testAttemptIds.map((testAttemptId) => {
      this.classroomsService.invigOpenCloseTestAttemptForStudents(testAttemptId, isPausing, params).then(res => {
        if (!isPausing) {
          this.studentStates[student.uid].is_paused = 0;
        }
        console.log("attempt paused");
        // this.studentG9Connection.notifyStudentsOfSubsession([student.uid], !isPausing, subSession.caption);
      });
    }))
  }

  hasOldTestForm(student: IStudentAccount) {
    if(!this.studentStates) {
      return false;
    }
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    return !!studentState?.hasOldTestForm;
  }

  isStudentWarning(student: IStudentAccount) {
    // todo:CONSISTENCY not sure why there is so much abed specific stuff here

    // check if they require softlock or not
    const studentSocketState = this.getStudentSocketState(student);
    // case where softlock and they're offline
    if (studentSocketState && !this.isStudentOnline(student)) {
      return false;
    }

    if (this.isABED() && this.isStudentOnline(student) && this.studentLockNotification.get(student.uid)){
      for(let i = 0; i < this.subSessions.length; i++){
        if(!this.isSubSessionSubmitted(student, i)){
          return true;
        }
      }
    }

    if (!this.isABED() && this.studentLockNotification.get(student.uid)) {
      return true;
    }

    // give them lock notification if online and has softlock requirements
    if (studentSocketState && studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
      if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
        this.studentLockNotification.set(student.uid, true);
        return true;
      }
    }
    // case where not softlock and they're offline or they're dismissing warning notif
    return false;
  }

  numStudentsPaused(){
    let count = 0
    try {
      for (let student of this.studentList){
        if (this.isStudentPaused(student)) { // && this.isStudentOnline(student)
          let isAllSessionsSubmitted = true;
          for(let i = 0; i < this.subSessions.length; i++){
            if(!this.isSubSessionSubmitted(student, i)){
              isAllSessionsSubmitted = false;
            }
          }
          if (!isAllSessionsSubmitted){
            count ++;
          }
        }
      }
    } catch (e) { }
    return count;
  }

  isStudentPausedTA(student: IStudentAccount) {
    // Based on Test_attempt table

    // check if they require softlock or not
    const studentSocketState = this.testAttemptSoftLockStatus[student.uid];
    // case where softlock is diabsled
    if (studentSocketState && studentSocketState.is_soft_lock_disabled) {
      this.studentLockNotification.set(student.uid, false);
      return false;
    }
    // give them lock notification if online and has softlock requirements
    if (studentSocketState && studentSocketState.is_paused) {
      this.studentLockNotification.set(student.uid, true);
      return true;
    }
   
    this.studentLockNotification.set(student.uid, false);
    return false;
  }


  isStudentPaused(student: IStudentAccount) {
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    let isStudentPaused = false;
    if (this.studentPauseNotification.has(student.uid) && this.studentPauseNotification.get(student.uid)) {
      isStudentPaused = true;
    } else if (studentState) {
      isStudentPaused = studentState.is_paused === 1 ? true : false;
    }
    return isStudentPaused;
  }

  isStudentWarningOrPaused(student: IStudentAccount) {
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    var isStudentPaused = false;
    if(studentState){
      isStudentPaused = studentState.is_paused === 1 ? true : false;
    }
    if (isStudentPaused) {
      return true;
    }
    if (!this.isStudentOnline(student)) {
      return false;
    }
    const studentSocketState = this.getStudentSocketState(student);
    // console.log(studentSocketState)
    if (studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
      //console.log('inside first')
      //console.log(this.studentLockState.has(student.uid),this.studentLockState.get(student.uid))
      if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
        this.studentLockNotification.set(student.uid, true);
        // //console.log('inside second')
        // const subSessions = this.studentStates[student.uid].subSessions;
        // const attemptId = this.studentStates[student.uid].attempt_id;
        // const data = [""];
        // Promise.all(
        //   subSessions.map(async (subSession, idx) => {
        //     //console.log(this.getStudentSessionLockState(student,idx),'session lock state',idx)
        //     if (!this.getStudentSessionLockState(student, idx)) {
        //       this.studentLockNotification.set(student.uid, true);
        //     }
        //   })
        // ).then(res => {
        //   this.studentLockState.set(student.uid, true);
        // });
      }
    }
    //console.log('got here',this.studentLockNotification.get(student.uid))
    return this.studentLockNotification.get(student.uid);
  }
  isStudentSoftLockWarning(student) {
    const studentSocketState = this.getStudentSocketState(student);
    if (this.isStudentWarningOrPaused(student) && studentSocketState.softLock <= 2) {
      return true;
    }
    return false;
  }
  
  isStudentSoftLockStrongWarning(student) {
    const studentSocketState = this.getStudentSocketState(student);
    if (this.isStudentWarningOrPaused(student) && studentSocketState.softLock > 2) {
      return true;
    }
    return false;
  }

  updateStudentTestForm(student: IStudentAccount) {
    const uid = student.id || student.uid;

    const alreadyBegunMessage = 'msg_old_test_form_begun';
    if(!this.studentStates[uid].canUpdateTestForm) {
      this.loginGuard.quickPopup(alreadyBegunMessage)
      return;
    }

    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('msg_old_test_form'),
      confirm: () => {
        this.auth.apiPatch(this.routes.EDUCATOR_UPDATE_TEST_FORM, student.id || student.uid, {}, {query: {test_session_id: this.testSessionId, schl_class_group_id: this.classroomsService.getClassroomById(this.classId).group_id}})
        .then(() => {
          this.studentStates[uid].hasOldTestForm = false;
        }).catch((e) => {

          let errMessage;
          switch(e.message) {
            case 'ALREADY_BEGUN':
              errMessage = alreadyBegunMessage;
            default:
              errMessage = 'msg_old_test_form_err';
          }
          this.loginGuard.quickPopup(errMessage)
        })
      }
    });
  }

  // this.studentSubSessionToRestore.set(student.uid, idx);
  // this.pauseStudentAttempt(attemptId, true, student, idx);

  openSoftLockNotification(student: IStudentAccount) {
    this.currentStudent = student;
    // console.log(student)
    this.openSoftlockModal.emit({openModal: true, uid: student.uid});
  }

  openDismissModal() {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_dismiss"),
      confirm: () => {
        this.studentLockNotification.set(this.currentStudent.uid, false);
        this.resetStudentSoftLock(this.currentStudent);
        this.currentStudent = null;
      }
    });
  }

  async processSingleUnpauseNotification(student: IStudentAccount) {
    this.resetStudentSoftLock(student);
    const subSessions = this.studentStates[student.uid].subSessions;
    const testAttemptIds = [];
    subSessions.forEach((ss: any) => {
      if (!testAttemptIds.includes(ss.test_attempt_id)) {
        testAttemptIds.push(ss.test_attempt_id);
      }
    });
    //console.log('checking index to restore',idxToRestore, typeof(idxToRestore))
    this.pauseStudentAttempt(testAttemptIds, false, student);
    this.studentLockState.set(student.uid, false);
    this.studentPauseNotification.set(student.uid, false);
    this.currentStudent = null;
    return;
  }

  async processAllUnapusedNotifications(){
    for(let student of this.studentList){
      if(this.isStudentPaused(student)){
        this.processSingleUnpauseNotification(student);
      }
    }
    return;
  }

  openAndDismissUnpauseNotificationAll(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_unpause_all"),
      confirm: () => {
        this.processAllUnapusedNotifications().then(()=>{
          this.allowDisableSoftLockNotification();
        })
      },
      close:  async () => {
        setTimeout(()=>{
          this.allowDisableSoftLockNotification();
        }, 0)
      }
    });
  }

  allowDisableSoftLockNotification(){
    if(this.getAllowDisableLockAfterNotification() && !this.isUserSoftLockNotified){
      this.notifyUserSoftLock.emit();
      this.loginGuard.confirmationReqActivate({
      btnProceedConfig: {
        caption: this.lang.tra("lbl_confirm")
      },
      caption: "You may disable softlock by pressing confirm button or disable softlock later by 'disable softlock' button.",
      confirm: () => {
        this.disableSoftLock.emit()
      }
    });
    }
  }
  getAllowDisableLockAfterNotification(){
    return this.whiteLabelService.getSiteFlag("IS_ALLOW_DISABLE_SOFTLOCK_AFTER_NOTIFICATION")
  }
  
  openAndDismissUnpauseNotification(student: IStudentAccount) {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_unpause"),
      confirm: () => {
        this.processSingleUnpauseNotification(student).then(()=>{
          this.allowDisableSoftLockNotification();
        })
      },
      close:  async () => {
        setTimeout(()=>{
          this.allowDisableSoftLockNotification();
        }, 0)
      }
    });
  }

  async unPauseStudentSoftLock() {
    await this.commonPauseSequence(() => this._unPauseStudentSoftLock())
  }

  async _unPauseStudentSoftLock(){
    const student = this.currentStudent
    const attempt = this.testAttemptSoftLockStatus[student.uid] // should be based on API ?
    if(attempt && attempt.is_paused){
      // Unpause
      const params = this.classroomsService.constructClassGroupIdQuery(this.classId)
      this.studentSoftLock.invigUnpauseTestAttemptForStudents(attempt.test_attempt_id, params)
        .then((res) => {
          this.testAttemptSoftLockStatus[student.uid].is_paused = 0
        })
        .catch((e) => console.log("STUDENT_ATTEMPT_UNPAUSE_FAILED",e))
    }
  }


  async disableStudenSoftLockForAttempt(){
    this.commonPauseSequence(() => this._disableStudenSoftLockForAttempt())
  }
  async _disableStudenSoftLockForAttempt(){
    const student = this.currentStudent
    const attempt = this.testAttemptSoftLockStatus[student.uid] // should be based on API ?
    if(attempt && attempt.is_paused){
      // Unpause
      const params = this.classroomsService.constructClassGroupIdQuery(this.classId)
      await this.studentSoftLock.invigDisableSoftLockForStudentAttempt(attempt.test_attempt_id, params)
        .then((res) => {
          this.testAttemptSoftLockStatus[student.uid].is_paused = 0
          this.testAttemptSoftLockStatus[student.uid].is_soft_lock_disabled = 1
        })
        .catch((e) => console.log("SOFT_LOCK_DISABLE_FAILED", e))
    }
  }

  async commonPauseSequence(pauseMethod, isSilent?:boolean){
    if (this.isNBED() || this.isMBED()){
      let studentName = this.getStudentName(this.currentStudent)  //'Cet⸱te élève'
      // try {
      //   studentName= this.currentStudent.first_name
      // } catch(e) {}
      await pauseMethod();
      const msg = this.lang.tra("student_alert_nbed")
      alert(studentName+ msg) 
    }
    else{ 
      if(!isSilent){
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra("msg_navigate_pause"),
          confirm: () => pauseMethod()
        });
      }else{
        await pauseMethod();
      }
    }
  }

  getStudentName = (student) => {
    let studentName = this.lang.c() === 'fr' ? 'Cet⸱te élève' : 'This Student'
    if(student && student.first_name) {
      studentName = student.first_name
    }
    return studentName
  } 


  async pauseStudentAndDismissSoftLockNotification(student?: IStudentAccount, isSilent?: boolean) {
    await this.commonPauseSequence(() => this._pauseStudentAndDismissSoftLockNotification(student), isSilent)
  }

  async _pauseStudentAndDismissSoftLockNotification (studentAcc?: IStudentAccount) {
    const student = studentAcc ? studentAcc : this.currentStudent;
    const studentSocketState = this.getStudentSocketState(student);
    // console.log(studentSocketState)
    if (studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
      //console.log('inside first')
      //console.log(this.studentLockState.has(student.uid),this.studentLockState.get(student.uid))
      if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
        //console.log('inside second')
        const subSessions = this.studentStates[student.uid].subSessions;
        const testAttemptIds = [];
        subSessions.forEach((ss: any) => {
          if (!testAttemptIds.includes(ss.test_attempt_id)) {
            testAttemptIds.push(ss.test_attempt_id);
          }
        });
        Promise.all(
          subSessions.map(async (subSession, idx) => {
            //console.log(this.getStudentSessionLockState(student,idx),'session lock state',idx)
            if (!this.getStudentSessionLockState(student, idx) || this.isStudentBetweenSubsessions(student, idx)) {
              this.studentSubSessionToRestore.set(student.uid, idx);
              await this.pauseStudentAttempt(testAttemptIds, true, student);
            }
          })
        ).then(res => {
          this.studentLockNotification.set(student.uid, false);
          this.studentPauseNotification.set(student.uid, true);
          this.studentLockState.set(student.uid, true);
          this.currentStudent = null;
        });
      }
    }
  }
  

  // deprecated
  // dismissSoftLockNotification(student: IStudentAccount) {
  //   this.loginGuard.confirmationReqActivate({
  //     caption: this.lang.tra("msg_student_navigation_warning"),
  //     confirm: () => {
  //       this.resetStudentSoftLock(student);
  //       const subSessions = this.studentStates[student.uid].subSessions;
  //       const attemptId = this.studentStates[student.uid].attempt_id;
  //       const idxToRestore = this.studentSubSessionToRestore.get(student.uid);
  //       //console.log('checking index to restore',idxToRestore, typeof(idxToRestore))
  //       this.pauseStudentAttempt(attemptId, false, student, idxToRestore);
  //       this.studentLockState.set(student.uid, false);
  //       this.studentLockNotification.set(student.uid, false);
  //       // if(idxToRestore != null && idxToRestore != undefined){
  //       //   console.log('in here')
  //       //   this.setStudentsLocks(false, [student], parseInt(idxToRestore))
  //       //   this.studentLockState.set(student.uid,false)
  //       // }
  //       // else{
  //       //   subSessions.forEach(async(subSession,idx) =>{
  //       //     if (subSession.started_on != null && subSession.is_submitted != 1){
  //       //       this.setStudentsLocks(false, [student], idx)
  //       //       this.studentLockState.set(student.uid,false)
  //       //     }
  //       //   })
  //       // }

  //       //this.isSoftLockReset = true;
  //     }
  //   });
  // }
  resetStudentSoftLock(student: IStudentAccount) {
    this.resetSoftLock.emit(student.uid);
  }

  //only allow the functionality of reject all if we have any students.
  rejectAllWalkin() {
    if(!this.studentList.length) return;
    this.rejectAllWalkStudents.emit();
  }

  //only allow the functionality of accept all if we have any students.
  acceptAllWalkin() {
    if(!this.studentList.length) return;
    this.loginGuard.confirmationReqActivate(
      {
        caption: this.lang.tra('abed_wr_accept_all_warning'),
        confirm: () => {this.acceptAllWalkStudents.emit();}
      }
    )
  }

  //put the accept functionality behind an 'are you sure' check, since the consequence of this functionality is converting the student from walk-in to regular
  acceptWalkin(student: IStudentAccount) {
    this.loginGuard.confirmationReqActivate(
      {
        caption: this.lang.tra('abed_wr_accept_warning'),
        confirm: () => {this.acceptWalkStudent.emit(student.uid);}
      }
    )
  }

  //if student is already rejected then ignore the request. The UI is clear enough, we don't need a pop-up message.
  rejectWalkin(student: IStudentAccount) {
    if(student.RejectedByTeacher) return;
    this.rejectWalkStudent.emit(student.uid);
  }

  isStudentSubSessionActive(student: IStudentAccount, subSessionIndex: number) {
    // this should be using the online status
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }

    return !this.getStudentSessionLockState(student, subSessionIndex);
  }
  isStudentSubSessionNotStarted(student: IStudentAccount, subSessionIndex: number) {
    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);

    const studentSocketState = this.getStudentSocketState(student);
    if (studentSocketState && studentSocketState.questionCaption !== undefined && studentSocketState.stageIndex !== undefined) {
      return false;
    }

    return !(subSessionstate && subSessionstate.started_on);
  }

  isStudentSubsessionNotStartedByQuestion(student: IStudentAccount, question_id: number){
    const tsssId = this.getStudentScanTsssId(student, question_id)
    const subSessionIndex = this.subSessions.findIndex(s => s.id == tsssId)
    return this.isStudentSubSessionNotStarted(student, subSessionIndex)
  }

  isSubSessionSubmitted(student, subSessionIndex) {
    const studentSocketState = this.getStudentSocketState(student);
    if (studentSocketState && studentSocketState[`submitted${subSessionIndex}`]) {
      return true;
    }

    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);
    return subSessionstate && subSessionstate.is_submitted;
  }
  isStudentSubSessionSubmitted(student: IStudentAccount, subSessionIndex: number) {
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }

    if(this.isNBED() || this.isMBED()){
      if(this.testAttemptSoftLockStatus && this.testAttemptSoftLockStatus[student.uid]){
        // few scenarios:
        // 1. what happens when there are multiple subsessions? #TODO: find a way to get them with TA status
        return this.testAttemptSoftLockStatus[student.uid].is_submitted
      }
    } 

    // EQAO and ABED use this to check if the subsession has been submitted for the student.
    return this.isSubSessionSubmitted(student, subSessionIndex);
  }

  showSoftLockWarning(student) {
    this.loginGuard.quickPopup("This student has left the test. You can lock the test session or allow them to continue with the test.");
    this.updateSoftLockRules(student);
  }

  updateSoftLockRules(student) {
    this.updateStudentSoftLock.emit(student);
  }

  getStudentSubSessionStatus(student: IStudentAccount, subSessionIndex: number) {
    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return "Ongoing in Another Session";
    }
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return "lbl_submitted_other_session";
    } else {
      const lockState = this.getStudentSessionLockState(student, subSessionIndex);
      const studentSocketState = this.getStudentSocketState(student);
      
      const renderPosition = (stageIndex, questionCaption, questionIndex?) => {
        let eqao_title: string = "eqao_title_stage";
        let pj_title_stageIndex: string = '';
        // todo:DB_DATA_MODEL
        if (student.eqao_is_g3 === '1' || student.eqao_is_g6 === '1') {
          if(this.isPJSampleTest()) {
            if(subSessionIndex == 0 || subSessionIndex == 1) {
              pj_title_stageIndex = PJSampleLangTitle[stageIndex];
            }
            if(subSessionIndex == 2 || subSessionIndex == 3) {
              pj_title_stageIndex = PJSampleMathTitle[stageIndex];
            }   
          }
          if(this.isPJOperationalTest()) {
            if(subSessionIndex == 0 || subSessionIndex == 1 || subSessionIndex == 2 || subSessionIndex == 3) {
              pj_title_stageIndex = PJOperationalLangTitle[stageIndex];
            }
            if(subSessionIndex == 4 || subSessionIndex == 5 || subSessionIndex == 6 || subSessionIndex == 7) {
              pj_title_stageIndex = PJOperationalMathTitle[stageIndex];
            }   
            if(subSessionIndex == 8) {
              pj_title_stageIndex = 'pj_session_q';
            }
          }
        }
        if (student.eqao_is_g9 === "1") {
          eqao_title = "eqao_title_stage";
        }
        if (student.eqao_is_g10 === "1") {
          eqao_title = "eqao_title_section";
        }

        if(!questionCaption) {
          questionCaption = `Question ${questionIndex + 1}`; //For the transition period of the switchover to questionCaption, where questionCaption may not be initialized
        }
        if(this.isPrimaryOrJunior()) {
          return `${this.lang.tra(pj_title_stageIndex)}, ${questionCaption}`;
        } 
        return `${this.lang.tra(eqao_title)} ${stageIndex + 1}, ${questionCaption}`;
      };
      if (subSessionstate) {
        if (this.isSubSessionSubmitted(student, subSessionIndex)) {
          const subSessionSlug = this.subSessions[subSessionIndex].slug;
          if(this.isWritingPaper(student) && subSessionSlug !== "session_q" && subSessionSlug !== "lang_session_d" && this.isPJOperationalTest() && !this.isPJMathSession(this.subSessions[subSessionIndex].slug)) {
            switch(this.getStudentQuestionResponseInfo(student.uid, subSessionIndex)) {
              case StudentQuestionResponseInfoStatusType.ScanConfirmed:
                return 'lbl_scan_confirmed';
              case StudentQuestionResponseInfoStatusType.ScanUploaded:
                return 'lbl_scan_uploaded';
            }
            this.isWritingPaperSubmitted = true;
            return "lbl_submitted_scan_required";
          }
          return this.subSessions[subSessionIndex].slug === "session_q" ? "lbl_status_submitted" : "lbl_submitted";
        } else if (studentSocketState?.stageIndex != null && studentSocketState?.questionCaption != null) {
          return renderPosition(studentSocketState.stageIndex, studentSocketState.questionCaption, studentSocketState.questionIndex );
        } else if (!subSessionstate.started_on) {
          if (lockState) {
            // console.log(this.subSessions)
            // console.log(this.subSessions[subSessionIndex].date_time_start)
            return this.subSessions[subSessionIndex].slug === "session_q" ? "lbl_not_started_2" : "lbl_not_started";
            // let subSessionDate = this.subSessions[subSessionIndex].date_time_start;
            // if(subSessionDate){
            //   const startDate = new Date(subSessionDate);
            //   const timeConvert = moment.tz(startDate, moment.tz.guess())
            //   const startDay = timeConvert.format(this.lang.tra('datefmt_day_month'));
            //   const startTime = timeConvert.format(this.lang.tra('h:mm A'));
            //   return `${startDay},${startTime}`;
            // }
          } else {
            return "lbl_ready_to_start";
          }
        } else {
          const studentState = this.getStudentState(student);
          if(subSessionstate?.sections_allowed?.includes(studentState?.section_index)) {
            return renderPosition(studentState.section_index, studentState.question_caption, studentState.question_index,);
          } else {
            return 'lbl_ready_to_start';
          }
          // if (lockState){
          //   return 'lbl_locked';
          // }
          // return 'lbl_inprogress';
        }
      }
    }

    // return 'lbl_not_started';
    return "...";
  }

  isPJMathSession(sessionSlug: string){
    if(sessionSlug == 'math_stage_1' || sessionSlug == 'math_stage_2' || sessionSlug == 'math_stage_3' || sessionSlug == 'math_stage_4') {
      return true;
    }
    return false;
  }

  showEditModal(subSessionIndex) {
    const subSession = this.subSessions[subSessionIndex];
    this.openEditModal.emit(subSession);
  }
  canSubSessionStart(subSessionIndex) {
    if (this.subSessions[subSessionIndex].slug === 'session_q') {
      return true;
    }
    const startDate = this.subSessions[subSessionIndex].date_time_start;
    const duration = this.subSessions[subSessionIndex].duration_hours;

    if (this.startSession(startDate, duration, subSessionIndex)) {
      return true;
    }
    return false;
  }

  startSession(dateStr, duration, subSessionIndex) {
    if (dateStr) {
      if (this.isSessionInProgress(dateStr, duration)) {
        const currDate = new Date();
        const selectedDate = new Date(dateStr);
        const endTime = selectedDate.getTime() + duration * 60 * 60 * 1000;
        const thirtyMinsIn = selectedDate.getTime() + 30 * 60000;
        const oneHourLeft = endTime - 1 * 60 * 60 * 1000;
        if (currDate.getTime() >= thirtyMinsIn && !this.isDismissedSessionNotification(subSessionIndex)) {
          // if (subSessionIndex === 0) {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_30_mins_warning_session_a"));
          // } else {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_30_mins_warning_session_b"));
          // }
          this.setDismissedSessionNotification(subSessionIndex);
        }

        if (currDate.getTime() >= oneHourLeft && !this.isDismissedSessionNotificationWarning(subSessionIndex)) {
          // if (subSessionIndex === 0) {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_one_hour_left_session_a"));
          // } else {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_one_hour_left_session_b"));
          // }
          this.setDismissedSessionNotificationWarning(subSessionIndex);
        }
        return true;
      } else {
        if (this.isSessionStarted(dateStr, duration)) {
          return true;
        }
        return false;
      }
    }
    return false;
  }
  isSessionStarted(dateStr, duration) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    let sessionEnd = selectedDate.getTime() + duration * 60 * 60 * 1000;
    if (currDate.getTime() >= selectedDate.getTime()) {
      return true;
    }
    return false;
  }

  isABEDSessionStarted(dateStr){
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    if (currDate.getTime() >= selectedDate.getTime()) {
      return true;
    }
    return false;
  }

  isDateTimeInThePast(UTCDateTime: string): boolean
  {
    const inputDTMoment = moment(UTCDateTime).utc(); // in UTC
    const nowDTMoment = moment().utc(); // in UTC
    return inputDTMoment.isBefore(nowDTMoment);
  }

  isSessionInProgress(dateStr, duration) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    let sessionEnd = selectedDate.getTime() + duration * 60 * 60 * 1000;
    if (currDate.getTime() >= selectedDate.getTime() && currDate.getTime() < sessionEnd) {
      return true;
    }
    return false;
  }

  isDismissedSessionNotification(subSessionIndex) {
    if (subSessionIndex == 0) {
      return this.isDismissedSessionANotfifcation;
    }
    return this.isDismissedSessionBNotification;
  }
  isDismissedSessionNotificationWarning(subSessionIndex) {
    if (subSessionIndex == 0) {
      return this.isDismissedSessionANotfifcationWarning;
    }
    return this.isDismissedSessionBNotificationWarning;
  }
  setDismissedSessionNotificationWarning(subSessionIndex) {
    if (subSessionIndex == 0) {
      return (this.isDismissedSessionANotfifcationWarning = true);
    }
    return (this.isDismissedSessionBNotificationWarning = true);
  }

  setDismissedSessionNotification(subSessionIndex) {
    if (subSessionIndex == 0) {
      return (this.isDismissedSessionANotfifcation = true);
    }
    return (this.isDismissedSessionBNotification = true);
  }
  getStudentSessionTime(student: IStudentAccount, subSessionIndex: number) {
    const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
    if (subSessionState && subSessionState.started_on) {
      return subSessionState._timeSpent;
    }
  }
  getStudentSessionTimeRemaining(student: IStudentAccount, _subSessionIndex: number = 0) {
    const studentState = this.getStudentState(student);
    if (!studentState) return '';

    // Use pre-calculated test attempt time remaining (calculated in refreshComputedState)
    const timeRemaining = studentState._testAttemptTimeRemaining || '?';
    console.log('getStudentSessionTimeRemaining - student uid', student.uid, 'timeRemaining', timeRemaining);

    let str = timeRemaining + ' minute(s)';
    if (studentState.time_ext_m) {
      str += ` [incl. ${studentState.time_ext_m}m ext.]`;
    }
    console.log('getStudentSessionTimeRemaining - final string', str);

    return str;
  }

  async updateSessionTimeExt(payload: ITimeExtUpdate, student: IStudentAccount, subSessionIndex: number = 0){
    const studentState = this.getStudentState(student); // todo:CONSISTENCY possible to have more than one attempt per student, this will break in sessions where students have more than one attempt
    const {time_ext_m} = await this.auth.apiPatch('public/educator/student-session-time', studentState.attempt_id, {
      updateOption: payload.updateOption,
      updateVal: payload.minutes,
    });
    studentState.time_ext_m = time_ext_m
    this.refreshComputedState();
    payload.resolve({time_ext_m});
  }

  getSubSessionScheduledTime(subSessionIndex) {
    let subSessionDate = this.subSessions[subSessionIndex].date_time_start;
    let duration = this.subSessions[subSessionIndex].duration_hours;
    if (subSessionDate) {
      const startDate = new Date(subSessionDate);
      const endDate = startDate.getTime() + duration * 60 * 60 * 1000;
      const timeConvert = moment.tz(startDate, moment.tz.guess());
      const endTimeConvert = moment.tz(endDate, moment.tz.guess());
      const startDay = timeConvert.format(this.lang.tra("datefmt_day_month"));
      let timezone = timeConvert.zoneAbbr();
      timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label');
      const startTime = `${timeConvert.format(this.lang.tra("timefmt_hour_time"))} ${timezone}`;
      const endTime = `${endTimeConvert.format(this.lang.tra("timefmt_hour_time"))} ${timezone}`;
      return {
        date: startDay,
        first_time: startTime,
        second_time: endTime
      };
    }
  }

  getPJSubSessionScheduledDate(subSessionIndex) {
    let subSessionDate = this.subSessions[subSessionIndex].date_time_start;
    if (subSessionDate) {
      const startDate = new Date(subSessionDate);
      const timeConvert = moment.tz(startDate, moment.tz.guess());
      const startDay = timeConvert.format(this.lang.tra("datefmt_day_month"));
      return {
        date: startDay,
      };
    }
    return null;
  }

  isStudentSubSessionEffectiveClosing(student: IStudentAccount, subSessionIndex: number) {
    const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
    const subSessionIndexPre = subSessionIndex - 1;
    const ssTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
    if (subSessionIndexPre >= 0) {
      const preSsTwtdarOrder = this.subSessions[subSessionIndexPre].twtdar_order;
      if(!this.isNoTdOrder || (ssTwtdarOrder === preSsTwtdarOrder)) {
        const subSessionStatePre = this.getStudentSubSessionState(student, subSessionIndexPre);
        const isCompleted = subSessionStatePre && subSessionStatePre.is_submitted;
        const isStarted = subSessionState && subSessionState.started_on;
        if (!isCompleted && !isStarted) {
          return true;
        }
      }
    }
    return false;
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() {
    return this.cModal().config;
  }

  isStudentSubSessionUpcoming(student: IStudentAccount, subSessionIndex: number, verbose = false) {
    const ssTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
    const prevSubSessionIndex = subSessionIndex - 1;

    if (prevSubSessionIndex >= 0) {
      const prevSsTwtdarOrder = this.subSessions[prevSubSessionIndex].twtdar_order;
      if(!this.isNoTdOrder || (prevSsTwtdarOrder === ssTwtdarOrder)) {
        if (this.checkOtherCompletedSessions(student.uid, this.subSessions[prevSubSessionIndex].slug)) {
          return false;
        }
        const subSessionState = this.getStudentSubSessionState(student, prevSubSessionIndex);
        const isCompleted = subSessionState && subSessionState.is_submitted;
        const isStarted = subSessionState && subSessionState.started_on;
        const isLocked = this.getStudentSessionLockState(student, prevSubSessionIndex);
        if (verbose) {
          console.log({
            targetSubSessionIndex: prevSubSessionIndex,
            isCompleted,
            isStarted,
            isLocked
          });
        }
        // if (isCompleted) { // CLOSER_LOOK_20210807 conflicted with the one below
        if (isCompleted || (isLocked && isStarted)) {
          return false;
        } else {
          return true;
        }
      }
    }
    return false;
  }

  isStudentSubSessionSurpassed(student: IStudentAccount, subSessionIndex: number) {
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return false;
    }
    if (this.isSubSessionSubmitted(student, subSessionIndex)) {
      return true;
    }
    if (this.getStudentSessionLockState(student, subSessionIndex)) {
      const currTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
      for (let i = subSessionIndex + 1; i < this.subSessions.length; i++) {
        const nextTwtdarOrder = this.subSessions[i].twtdar_order; //Assumes all subsessions with the same twtdar order are adjacent
        if(nextTwtdarOrder !== currTwtdarOrder) {
          break;
        }
        const subSessionState = this.getStudentSubSessionState(student, i);
        if ((subSessionState && subSessionState.started_on) || !this.getStudentSessionLockState(student, i)) {
          return true;
        }
      }
    }

    const isLastSubSession = subSessionIndex+1< this.subSessions.length && (!this.isNoTdOrder || (this.subSessions[subSessionIndex + 1].twtdar_order === this.subSessions[subSessionIndex].twtdar_order));
    if(isLastSubSession && !this.isStudentSubSessionUpcoming(student, subSessionIndex+1) && this.canSubSessionStart(subSessionIndex+1)){
      return true
    }

    return false;
  }
  isOtherSessionActive(student, subSessionIndex) {
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }
  }
  getStudentSessionLockState(student: IStudentAccount, subSessionIndex: number) {
    const studentState = this.getStudentState(student);
    if(this.isABED()){
      return this.isStudentSessionLocked(studentState, subSessionIndex) && !this.isSubSessionSubmitted(student, subSessionIndex);
    }else{
      return this.isStudentSessionLocked(studentState, subSessionIndex);
    }
  }
  isStudentSessionLocked(studentState: IStudentState, subSessionIndex: number) {
    const subSession = this.subSessions[subSessionIndex];
    if (studentState && subSession) {
      return !(studentState.active_sub_session_id === subSession.id);
    }
    return true;
  }
  isStudentBetweenSubsessions(student: IStudentAccount, subSessionIndex: number) {
    const studentState = this.getStudentState(student);
    const subSessions = studentState.subSessions;
    const prevSs = subSessionIndex === 0 ? null : subSessions[subSessionIndex - 1];
    const currSs = subSessions[subSessionIndex];

    if (!prevSs) {
      // check if first ss has been started
      if (!currSs.started_on) {
        return studentState.active_sub_session_id === null;
      }
    } else {
      // check if prev one is at least started and current one hasn't been started
      if (prevSs.started_on && !currSs.started_on) {
        return studentState.active_sub_session_id === null;
      } 
    }
    return false;
  }
  checkOtherCompletedSessions(uid, slug) {
    const completed = this.completedSubSessions.filter(session => {
      return session.student_uid === uid && session.session_type === this.activeSession.slug && session.sub_session_slug === slug && session.test_session_id != this.testSessionId;
    });
    return completed.length > 0;
  }
  checkOtherActiveSessions(uid, slug) {
    const active = this.activeSubSessions.filter(session => {
      return session.student_uid === uid && session.session_type === this.activeSession.slug && session.sub_session_slug === slug && session.test_session_id != this.testSessionId;
    });
    return active.length > 0;
  }
  getActiveSubSession(uid, slug) {
    const active = this.activeSubSessions.filter(session => {
      return session.student_uid === uid && session.sub_session_slug === slug;
    });
    return active[0];
  }
  cancelOtherSubSession(student, subSessionIndex) {
    const subSessionToCancel = this.getActiveSubSession(student.uid, this.subSessions[subSessionIndex].slug);
    const students = [student];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return this.classroomsService.invigOpenCloseSubSessionForStudents(subSessionToCancel.test_session_id, subSessionToCancel.active_sub_session_id, subSessionToCancel.twtdar_order, [student.uid], this.classId, true, params,this.isPrimaryOrJunior(),false).then(res => {
      const i = this.activeSubSessions.indexOf(subSessionToCancel);
      this.activeSubSessions.splice(i, 1);
      students.forEach(student => {
        const studentState = this.getStudentState(student);
        studentState.active_sub_session_id = null;
      });
      this.checkAllStudentSessionState();
      this.refreshComputedState();

      this.studentG9Connection.notifyStudentsOfSubsession([student.uid], false, subSessionToCancel.current_session, this.activeSession.slug, subSessionToCancel.slug);
    });
  }
  verifyPastDate(dateStr) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    if (currDate.getTime() > selectedDate.getTime()) {
      return true;
    }
    return false;
  }

  verifyUnsubmit(student: IStudentAccount, subSessionIndex: number){  
    this.unsubmitedStudent = null;
    this.unsubmitedSubSessionIndex = null;
    this.unsubmitedStudent = student;
    this.unsubmitedSubSessionIndex = subSessionIndex;
    const studentUID = student.uid;
    const studentRecord = this.studentStates[studentUID];
    if(studentRecord && studentRecord.is_ta_unsubmit_pending) {
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_existing_pending_unsubmission_request')), 0); 
      return;
    }
    if(this.isStudentOnline(student)){
      alert(this.lang.tra("lbl_verify_student_offline"))
    }else{
      this.verifyUnsubmitStep2(student, subSessionIndex);
    }
  }

  verifyUnsubmitStep2(student: IStudentAccount, subSessionIndex: number){
    const groupIdQuery = this.classroomsService.constructClassGroupIdQuery(this.classId);
    let params = {
      query: {
        school_class_group_id: groupIdQuery.query.school_class_group_id,
      }
    }
    this.auth
    .apiFind(this.routes.EDUCATOR_TEST_ATTEMPT_UNSUBMISSION_REASONS, params)
    .then(result => {
      if(result[0].testAttemptUnsubmissionReasons) {
        const props = {
          studentFirstName: student.first_name, 
          studentLastName: student.last_name, 
          studentOEN: student.abed_student_identification_number,
          testAttemptUnsubmissionReasons: result[0].testAttemptUnsubmissionReasons
        }
        let config = {
          caption: this.lang.tra("lbl_unsubmit_tass_confirm_abed", undefined, props),
          width: '35em',
          btnProceedConfig: {
            caption: this.lang.tra('btn_continue')
          },
          confirm: () => {
            this.unsubmitStudentModalStart(props);
          },
        }
        this.loginGuard.confirmationReqActivate(config)
      }
    }) 
  }

  unsubmitStudentModalStart(studentInfo){
    let config = {};  
    if(studentInfo) {
      config = studentInfo;
    }
    this.pageModal.newModal({
      type: TeacherModal.UNSUBMIT_STUDENT_MODAL,  
      config,
      finish: () => this.unsubmitStudentModalFinish
    });
  }

  unsubmitStudentModalFinish = (unsubmissionDetail:any) => {
    this.unsubmit(this.unsubmitedStudent, this.unsubmitedSubSessionIndex, unsubmissionDetail);
    this.pageModal.closeModal();
  }

  getMaxSubSession (activeSessionSlug: ASSESSMENT) {
    // todo:DB_DATA_MODEL allowed sessions
    return 1;
    // switch(activeSessionSlug){
    //   case ASSESSMENT.ABED_OPERATIONAL:
    //   case ASSESSMENT.ABED_PWR:
    //     return 1;
    //   case ASSESSMENT.OSSLT_OPERATIONAL:
    //   case ASSESSMENT.G9_OPERATIONAL:
    //   case ASSESSMENT.TCN_OPERATIONAL:
    //   case ASSESSMENT.TCLE_OPERATIONAL:
    //   case ASSESSMENT.SCIENCES8_OPERATIONAL:
    //     return 2;
    //   case ASSESSMENT.PRIMARY_OPERATIONAL:
    //   case ASSESSMENT.JUNIOR_OPERATIONAL:
    //     return 8;
    //   default:
    //     return 0
    // }
  }

  showUnsubmit(student: IStudentAccount, subSessionIndex: number){
    if (this.isForceUnsubmitAbility){
      return true
    }
    else if (!this.isUnsubmitBtnVisiable || !this.isStudentSubSessionSurpassed(student, subSessionIndex)) {
      return false
    }
    return true;
  }

  showAbsentBtn(){
    if (this.testSessionId && !this.isWalkinList){
      if (this.whiteLabelService.getSiteFlag(WL_SITE_FLAGS.IS_INVIG_ABSNC)){
        return true
      }
    }
    return false
  }

  isAbsent(student:IStudentAccount){
    const studentState = this.getStudentState(student)
    return studentState.isAbsent;
  }

  currentStudentAbsentMarks = new Map();
  async toggleAbsent(student:IStudentAccount){
    const test_session_id = this.testSessionId;
    const schl_class_group_id = this.activeClassroom.group_id;
    const studentState = this.getStudentState(student)
    this.currentStudentAbsentMarks.set(student.uid, true)
    if (!studentState.isAbsent){
      await this.auth.apiPatch(
        'public/educator/student/session-absence', 
        student.uid,
        {},
        {query: {schl_class_group_id, test_session_id}}
      )
      studentState.isAbsent = true;
    }
    else{
      await this.auth.apiRemove(
        'public/educator/student/session-absence', 
        student.uid,
        {query: {schl_class_group_id, test_session_id}}
      ) 
      studentState.isAbsent = false;
    }
    this.currentStudentAbsentMarks.set(student.uid, false)
  }

  isMarkingAbsence(student:IStudentAccount){
    return !!student.__isMarkingAbsence;
  }

  unsubmit(student: IStudentAccount, subSessionIndex: number, config:any){
    const test_session_id = this.testSessionId
    const studentStates = this.studentStates[student.uid];
    if(studentStates === undefined || studentStates.attempt_id === undefined ||  studentStates.attempt_id === null){
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_error')), 0);
      return
    }
    const attemptId = studentStates.attempt_id
    const data = {
      testSessionId:test_session_id, 
      studentId: student.uid, 
      subSessionIndex:subSessionIndex, 
      reasonId: config.unsubmitReason,
      reasonText: config.reasonText,
      isAllowAutoUnsubmit: config.isAllowAutoUnsubmit,
      isSecreteUser:this.isSecreteUser
    }
    const school_class_group_id = this.classroomsService.getClassroomById(this.classId).group_id
    const params = { query : {school_class_group_id}}
    this.g9DemoData.setIsUnsubmitting(true);
    this.auth.apiPatch(this.routes.EDUCATOR_STUDENT_TEST_ATTEMPT, attemptId, data, params)
      .then((res) => {
        this.reloadSessionInfo.emit(res);
        this.isUnsubmitBtnVisiableChange.emit(false);
      }).catch((err:Error)=>{
        this.g9DemoData.setIsUnsubmitting(false);
        if(err.message.includes("REQ_PARAMS_MISS")||err.message.includes("Error_FECTCHING_DATA")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_error')), 0);
        }
        if(err.message.includes("SELECTED_CATEGORY_MISS")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('alert_submit_student_assessment2_empty2')), 0);
        }
        if(err.message.includes("UNSUBMIT_TIME_EXPIRE")){
          //setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_time_expire')), 0);
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_time_expire_168')), 0);
        }
        if(err.message.includes("UNSUBMIT_STAGE_PASSED")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_stage_passed')), 0);
        }
        if(err.message.includes("INVALID_SECRETE_USER")){ 
          setTimeout(() => this.loginGuard.quickPopup("Invalid Secrete User"), 0);  //this message is for support when they try to unsubmit and have wrong secrete code 
        }
        if(err.message.includes("ACTIVE_SUB_SESSION_NOT_NULL")){ 
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_active_sub_session_not_null')), 0); 
        }
        if(err.message.includes("EXISTING_PENDING_UNSUBMISSION_REQUEST")){ 
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_existing_pending_unsubmission_request')), 0); 
        }
      })
  }

  showResponseType() {
    // todo:DB_DATA_MODEL
    return ['EQAO_G3', 'EQAO_G6'].includes(this.activeClassroom.curricShort) && (this.isManagingStudents || this.activeSession?.slug?.includes('OPERATIONAL'))
  }

  isClassABEDG12() {
    // TODO: change to using a flag potentially
    // todo:DB_DATA_MODEL
    // see: https://bubo.vretta.com/vea/project-management/vretta-project-notes/vea-abed/-/issues/2863
    return this.activeClassroom.curricShort === AssessmentCode.ABED_GRADE_12;
  }

  isDiplomaExamClassroom() {
    return this.activeClassroom.foreign_id != null && this.activeClassroom.foreign_id != 
    "";
  }

  filterStudentsToDisplay() {
    // todo:CONSISTENCY we are not intending to filter students based on their registered exams are we...
    if (this.showOnlyRegisteredStudents) {
      this.studentsToDisplay = (this.studentList || []).filter(student => { 
        const matchingExam: any[] = student.diplomaExamInfo.filter(exams => { // todo:DB_DATA_MODEL if we keep this code, it should not be ABED specific
          if ((+exams.test_window_id) === +this.activeClassroom.test_window_id){
            if (exams.type_slug === this.activeClassroom.foreign_id){
              return true;
            }
          }
        })
        return matchingExam.length !== 0;
      });   
    }
    else {
      this.studentsToDisplay = this.studentList;
    }
    this.sortedStudentList = this.sortStudents();
  }

  onSortOptionSelected(sortKey: string) {
    this.selectedSortOption = sortKey;
    this.sortedStudentList = this.sortStudents(); // Update the sorted list
    console.log(this.selectedSortOption)
    console.log(this.sortedStudentList)
  }

  getSortOptionLabel(key: string): string {
    const selectedOption = this.sortOptions.find(option => option.key === key);
    return selectedOption ? selectedOption.label : 'Sort by';
  }

  get filteredSortOptions() {
    return this.sortOptions.filter(option => {
      if ((option.key === 'lockAsc' || option.key === 'lockDesc') && (!this.isWalkinList && !this.isManagingStudents)) {
        return true;
      }
      return option.key !== 'lockAsc' && option.key !== 'lockDesc';
    });
  }
  
  sortStudents(): IStudentAccount[] {
    switch (this.selectedSortOption) {
      case 'asnAsc':
        return [...this.studentsToDisplay].sort((a, b) => this.getSecretId(a) - this.getSecretId(b));
      case 'asnDesc':
        return [...this.studentsToDisplay].sort((a, b) => this.getSecretId(b) - this.getSecretId(a));
      case 'nameAsc':
        return [...this.studentsToDisplay].sort((a, b) => {
          const lastNameComparison = a.last_name.localeCompare(b.last_name);
          if (lastNameComparison !== 0) {
            return lastNameComparison; // Primary sort by last_name
          }
          const firstNameComparison = a.first_name.localeCompare(b.first_name);
          if (firstNameComparison !== 0) {
            return firstNameComparison; // Secondary sort by first_name
          }
          return 0; // maintain original order
        });
      case 'nameDesc':
        return [...this.studentsToDisplay].sort((a, b) => {
          const lastNameComparison = b.last_name.localeCompare(a.last_name);
          if (lastNameComparison !== 0) {
            return lastNameComparison; // Primary sort by last_name (descending)
          }
          const firstNameComparison = b.first_name.localeCompare(a.first_name);
          if (firstNameComparison !== 0) {
            return firstNameComparison; // Secondary sort by first_name (descending)
          }
          return 0; // Tertiary sort: maintain original order
        });
      case 'lockDesc':
        return [...this.studentsToDisplay].sort((a, b) => {
          const isALocked = this.isStudentLocked(a);
          const isBLocked = this.isStudentLocked(b);
          if (isALocked === isBLocked) {
            return this.getSecretId(a) - this.getSecretId(b); // Secondary sort by ASN ascending
          }
          return isALocked ? 1 : -1; // Unlocked first
        });
      case 'lockAsc':
        return [...this.studentsToDisplay].sort((a, b) => {
          const isALocked = this.isStudentLocked(a);
          const isBLocked = this.isStudentLocked(b);
          if (isALocked === isBLocked) {
            return this.getSecretId(a) - this.getSecretId(b); // Secondary sort by ASN ascending
          }
          return isALocked ? -1 : 1; // Locked first
        });
      default:
        return [...this.studentsToDisplay]; // Default case: no sorting
    }
  }

  isStudentLocked(student: IStudentAccount){
    for (let subSessionIndex = 0; subSessionIndex < this.subSessions.length; subSessionIndex++){
      if(this.getStudentSessionLockState(student, subSessionIndex)){
        return true;
      }
    }
    return false;
  }

  getStudentListToDisplay(): IStudentAccount[] {
    return this.sortedStudentList;
  }

  displayStudentDiplomaExams(student: any): string {
    // todo:DB_DATA_MODEL abed specific ... other groups dont have diplomas
    const exams = (student.diplomaExamInfo || []).filter(exams => 
    +exams.test_window_id === +this.activeClassroom.test_window_id);
    return exams.map(exam => exam.display_course_name).join(", ");
  }

  isWritingPaper(student: IStudentAccount) {
    const isCrScanDefault = this.g9DemoData.getPropVal(student, 'IsCrScanDefault', this.activeClassroom.curricShort);
    return this.onlineOrPaper.getPaperVal(isCrScanDefault, this.activeClassroom.curricShort);
  }
  getSecretIdSlug = () => {
    // todo:STREAMLINE_LOGIC todo:DB_DATA_MODEL
    if(this.isNBED() || this.isMBED()) return 'show_userId'
    if(this.isABED()) return this.whiteLabelService.getSiteText('student_ident_show');
    if(this.isSasnLogin) return 'show_sasn'
    return 'show_oen'
  }

  getSecretId = (student) => { //TODO: student : IStudentAccount  add other props
    // todo:STREAMLINE_LOGIC todo:DB_DATA_MODEL
    if(this.isNBED()) return student.nbed_user_id;
    if(this.isABED()) return student.StudentIdentificationNumber || student.course.StudentIdentificationNumber || student.course.TestTakerIdNumber; // todo:dbmodel should be using the identification type defined in the class
    if(this.isMBED()) return student.mbed_user_id;
    if(this.isSasnLogin) return student.SASN
    return student.eqao_student_gov_id
  }

  getSecretIdHeaderSlug = () => {
    // todo:STREAMLINE_LOGIC todo:DB_DATA_MODEL
    if(this.isNBED() || this.isMBED()) return 'lbl_userId';
    if(this.isABED()) return this.whiteLabelService.getSiteText('student_ident');
    if(this.isSasnLogin) return 'lbl_sasn';
    return 'lbl_oen'
  }

  // todo:STREAMLINE_LOGIC todo:DB_DATA_MODEL
  isNBED = () => this.whiteLabelService.getSiteFlag('IS_NBED')
  isABED = () => this.whiteLabelService.getSiteFlag('IS_ABED')
  isMBED = () => this.whiteLabelService.getSiteFlag('IS_MBED')
  isEQAO = () => this.whiteLabelService.getSiteFlag('IS_EQAO')

  renderSubSessionSlug(subSession:{slug:string}){
    if (this.whiteLabelService.getSiteFlag('IS_INVIG_SESSION_SINGLE')){
      return 'All Students'
    }
    return this.lang.tra(subSession.slug)
  }

  showScanInfo() {
    return this.isScanSession;
  }
  
  getSessionSlugOne(slug: string) {
    let targetSlug;
    let mappedSlug = ASSESSION_SLUG_MAPPING().find(record => record.source === slug);
    if(mappedSlug) {
      targetSlug = mappedSlug.target_1;
    }
    return targetSlug;
  }

  getSessionSlugTwo(slug: string) {
    // todo:STREAMLINE_LOGIC (why not got 1 to N instead of 1 to 2 etc)
    let targetSlug;
    let mappedSlug = ASSESSION_SLUG_MAPPING().find(record => record.source === slug);
    if(mappedSlug) {
      targetSlug = mappedSlug.target_2;
    }
    return targetSlug;
  }

  // todo:DB_DATA_MODEL define as a twtar prop if we need to, does not need to be hard coded against the slug, check what these are used for
  isPJOperationalTest(activeSessionSlug?: ASSESSMENT) {
    let asmtSlug = this.asmtSlug
    if(activeSessionSlug) {
      asmtSlug = activeSessionSlug
    }
    return (asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }
  isPJSampleTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE);
  }
  isPrimaryOrJunior() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }
  isG9OrOsslt() {
    const allowed = new Set([ASSESSMENT.G9_SAMPLE, ASSESSMENT.G9_OPERATIONAL, ASSESSMENT.OSSLT_SAMPLE, ASSESSMENT.OSSLT_OPERATIONAL , ASSESSMENT.TCLE_OPERATIONAL, ASSESSMENT.TCLE_SAMPLE, ASSESSMENT.TCN_OPERATIONAL, ASSESSMENT.TCN_SAMPLE, ASSESSMENT.SCIENCES8_OPERATIONAL, ASSESSMENT.SCIENCES8_SAMPLE, ASSESSMENT.MBED_SAMPLE])
    return allowed.has(this.asmtSlug)
    // return (this.asmtSlug === ASSESSMENT.G9_SAMPLE || this.asmtSlug === ASSESSMENT.G9_OPERATIONAL || this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE || this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL);
  }

  getStudentQuestionResponseInfo(studentUid: number, subSessionIndex: number): StudentQuestionResponseInfoStatusType {
    if(!this.isPJOperationalTest()) {
      // todo:DB_DATA_MODEL why?
      return this.studentQuestionResponseInfoStatusType.None;
    }
    if (!studentUid) {
      throw 'Missing Student UId';
    }
    if (!this.scanInfo.studentScanInfoMap) {
      return this.studentQuestionResponseInfoStatusType.Loading;
    }
    const crQuestionsBySec = this.scanInfo.getCrQuestions(studentUid);
    const subSession = this.subSessions[subSessionIndex];
    if (crQuestionsBySec && subSession) {
      const sessionsKeys = Object.keys(crQuestionsBySec).sort();
      const sessionKey = sessionsKeys[subSession.order];
      if (sessionKey) {
        const session = crQuestionsBySec[sessionKey];
        const quesIds = Object.keys(session);
        if (quesIds) {
          const question = session[quesIds[0]];
          if (question) {
            if (question.isConfirmed) {
              return this.studentQuestionResponseInfoStatusType.ScanConfirmed;
            } 
            if (question.responses && question.responses.length > 0) {
              return this.studentQuestionResponseInfoStatusType.ScanUploaded;
            }
          }
        }
      }
    }
    return this.studentQuestionResponseInfoStatusType.None;
  }


  isStudentScanUploadForced(student: IStudentAccount, question_id: number){
    if (!this.isStudentDetailAccess || !this.scanInfo.studentScanInfoMap){
      return false
    }
    const studentUid:number = student.uid;
    const crQuestions = this.scanInfo.getCrQuestions(studentUid);
    if(crQuestions){
      const question = crQuestions[question_id];
      return !!question.isForcedUpload
    }
  }

  getStudentScanTsssId(student: IStudentAccount, question_id: number){
    if (!this.isStudentDetailAccess || !this.scanInfo.studentScanInfoMap){
      return false
    }
    const studentUid:number = student.uid;
    const crQuestions = this.scanInfo.getCrQuestions(studentUid);
    if(crQuestions){
      const question = crQuestions[question_id];
      return question.tsssId
    }
  }

  isStudentScanUploaded(student: IStudentAccount, question_id: number): StudentQuestionResponseInfoStatusType {
    if (!this.isStudentDetailAccess) return StudentQuestionResponseInfoStatusType.NoAccess
    const subSessionstate = this.getStudentSubSessionState(student, 0); //0 for now as we have only one session. Need to make it support multiple sessions
    const isSubSessionSubmitted = this.isSubSessionSubmitted(student,0)
    if(subSessionstate) {
      if(!this.checkedStudentSubmissions[student.id]){
        if(isSubSessionSubmitted){
          this.scanInfo.loadScanInfo(this.testSessionId, this.activeClassroom.group_id)
          this.checkedStudentSubmissions[student.id] = true;
        }
      }
    }
    const studentUid:number = student.uid;
    if(!this.scanInfo.studentScanInfoMap){
      return this.studentQuestionResponseInfoStatusType.Loading;
    }
    const crQuestions = this.scanInfo.getCrQuestions(studentUid);
    if(crQuestions){
      const question = crQuestions[question_id];
      if(question){
        // supressing it for now
        // if(question.isConfirmed){ 
        //   return this.studentQuestionResponseInfoStatusType.ScanConfirmed;
        // }
        if(question.responses && question.responses.length > 0){
          return this.studentQuestionResponseInfoStatusType.ScanUploaded;
        }
        else{
          return this.studentQuestionResponseInfoStatusType.None;
        }
      }
    }
  }
  get studentQuestionResponseInfoStatusType() {
    return StudentQuestionResponseInfoStatusType;
  }

  isStudentScanInfoMapInitialized(): boolean {
    // todo:DB_DATA_MODEL should be coming from the framework
    if(this.isNBED() || this.isMBED()) return true // Temp returning True until clarification if it's required for NB or MB.
    if (this.scanInfo.studentScanInfoMap || !this.showScanInfo()) {
      return true
    }
    return false
  }

  isUmsubmitting(){
    return this.g9DemoData.getIsUnsubmitting();
  }

  renderAge(student){
    // todo:CONSISTENCY this seems a flimsy way of applying this business rule
    const val = student.DateofBirth;
    if(val){
      const age = moment().diff(moment(val), 'years');
      if(age >= 18) return '(18+)';
    }
  }

  getMissingUnconfirmedScansNum() {
    if(!this.scanInfo.studentScanInfoMap) {
      return  { missingScans:0, unconfirmedScans: 0 };;
    }
    let studentUIDs
    let missingScans = 0;
    let unconfirmedScans = 0;
    if(this.studentList.length > 0) {
      const students = this.studentList.map(student => {
        const studentSubSessionsState = this.getStudentState(student)['subSessions'];
        return {
          uid: student.id,
          studentSubSessionsState,
          isPaperFormatDefault: this.activeClassroom.curricShort === AssessmentCode.EQAO_G3 ? true : false
        };
      });
      if(students.length > 0) {
        students.forEach( student => {
          this.scanInfo.getMissingUnconfirmedScans(this.scanInfo.studentScanInfoMap, student, _ => {missingScans++;} , _ => {unconfirmedScans++;});
        })
      }
      return { missingScans, unconfirmedScans };
    }
  }

    /** Load and store a single response sheet for the student and question */
    async resetStudentResponseTemplate(student, item_id) {
      const classroom = this.classroomsService.getClassroomById(this.classId);
      const respSheetConfig: IGenRespSheetConfig = {
        testSessionId: +this.testSessionId,
        classroomId: +this.classId,
        schl_class_group_id: classroom.group_id,
        asmtSlug: this.asmtSlug,
        isIndividual: true,
        whitelabelContext: this.whiteLabelService.getWhitelabelFlag() as FLAGS
      }
      const studentUid = student.uid;
      respSheetConfig.uids = [studentUid];
      let sessionSlugs = this.scanInfo.getScanSlugs(studentUid).filter(slug => slug == item_id)
      respSheetConfig['scanSlugs'] = sessionSlugs;
      const isSasn = this.classroomsService.isSASNLogin(this.classId) ? 1 : 0;
      respSheetConfig['isSasn'] = isSasn;
      await this.scanInfo.getStudentResponseTemplates(respSheetConfig)
      .catch((e) => {
        console.log(e)
        console.error('ERROR_FETCHING_RESPONSE_SHEETS')
      });
    }

    /** Print a single response sheet for student and question */
    async printSingleScan(student, item_id){
      await this.resetStudentResponseTemplate(student, item_id);
      const studentScanInfo = this.scanInfo.currStudentResponseTemplates;
      const base64 = studentScanInfo.get(item_id);
      this.scanInfo.generateResponseSheetPdf(base64)
    }

    /** Print all response sheets for a student as one file */
    printScansForStudent(student){
      const classroom = this.classroomsService.getClassroomById(this.classId);
      const respSheetConfig: IGenRespSheetConfig = {
        testSessionId: +this.testSessionId,
        classroomId: +this.classId,
        schl_class_group_id: classroom.group_id,
        asmtSlug: this.asmtSlug,
        isIndividual: false,
        whitelabelContext: this.whiteLabelService.getWhitelabelFlag() as FLAGS
      }
      const isSasnSchool = this.classroomsService.isSASNLogin(this.classId);
      respSheetConfig.uids = [student.uid];
      respSheetConfig['scanSlugs']= this.scanInfo.studentScanInfoMap[student.uid].scanningQuesIds;
      respSheetConfig['isSasn'] = isSasnSchool ? 1 : 0;
      this.scanInfo.getClassResponseTemplate(respSheetConfig);
    }
}

