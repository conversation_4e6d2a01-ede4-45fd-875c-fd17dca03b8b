
export interface ISubSessionRecord {
  duration_hours: any;
  duration: any;
  date_time_start: any;
  caption: string;
  id: number;
  order: number;
  sections_allowed: number[];
  slug: string;
  twtdar_order: number;
  is_last: number;
}

export interface IReviewSSModalConfig {
  studentStates: any,
  studentList: any,
  subSessionIndex: number,
  isClosing: boolean,
  isAll: boolean
}

export interface IStudentState {
  is_paused: number;
  uid;
  attempt_id;
  active_sub_session_id;
  last_touch_on;
  section_index;
  question_index;
  question_caption;
  is_submitted;
  time_ext_m:number;
  isAbsent?: boolean;
  hasOldTestForm;
  canUpdateTestForm;
  // isOnline?:boolean, // appended
  _testAttemptTimeRemaining?: number; // mutate - test attempt level time remaining (like API)
  subSessions: Array<{
    is_submitted: number;
    last_locked_on: string;
    started_on: string;
    last_touch_on: string;
    subtracted_time: number;
    num_responses: number;
    time_ext_m: number;
    sections_allowed: number[];
    subsession_slug?: string;
    _timeSpent?: string; // mutate
    _timeRemaining?: number; // mutate
    _time_ext_m?: number; // mutate
  }>;
  is_ta_unsubmit_pending;
}