import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { IContentElementMcq } from '../../ui-testrunner/element-render-mcq/model';
import { IQuestionConfig } from '../../ui-testrunner/models';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { ITestDef } from '../../ui-testrunner/sample-questions/data/sections';
import { ICustomConfirmTestDialogData } from '../../ui-testrunner/test-runner/test-runner.component';
import { ISectionDef, ITestAttemptRes } from '../../ui-testtaker/view-tt-test-runner/view-tt-test-runner.component';
import { StudentG9ConnectionService } from '../student-g9-connection.service';
import { StudentG9DashboardService } from '../student-g9-dashboard.service';
import { parseAttemptLoadErrorMessage } from './util/parse-warning';
import { StyleprofileService } from '../../core/styleprofile.service';
import { StudentAssistiveTechService } from '../student-assistive-tech.service'
import { WhitelabelService } from '../../domain/whitelabel.service';
import { ASSESSMENT } from '../../ui-teacher/data/types';
import { StudentSoftLockService } from '../student-soft-lock.service';
import { ISsAttemptSlugMap, ISubSessionAttemptDef } from '../types';
import { mtz } from 'src/app/core/util/moment';
import { objectToNumMap } from 'src/app/core/util/arr';

@Component({
  selector: 'view-student-live-assess',
  templateUrl: './view-student-live-assess.component.html',
  styleUrls: ['./view-student-live-assess.component.scss']
})
export class ViewStudentLiveAssessComponent implements OnInit, OnDestroy {
 

  constructor(
    public lang: LangService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private routes: RoutesService,
    private auth: AuthService,
    private dash: StudentG9DashboardService,
    private router: Router,
    private route: ActivatedRoute,
    private styleProfile: StyleprofileService,
    private assisTech: StudentAssistiveTechService,
    public studentG9Connection: StudentG9ConnectionService,
    private whitelabel: WhitelabelService,
    private studentSoftLock: StudentSoftLockService
  ) { 
    dash.init();
  }

  userInfo;
  routeSub: Subscription;
  queryParamSub: Subscription;
  userSub: Subscription;
  studentSoftLockScreenFreezeSub: Subscription;
  studentConnScreenFreezeSub: Subscription;
  testSessionId: string;
  testSessionSlug: string;
  componentName: string;
  isFsa: boolean;
  asmtLabel: string;
  isShowingResults = false;
  questions: Map<number, any>;
  testTakerName: string;
  seenStudentOffScreenWarning: boolean = false;
  questionTitleMap:any;
  testForm: {
    currentTestDesign: ITestDef,
    questionSrcDb: Map<number, any>,
    questionStates: { [key: string]: any },
    testLang: string,
  };
  currentStudentPosition = {
    stageIndex: null,
    questionCaption: null
  };
  testAttemptInfo: ITestAttemptRes;
  public currentWarning = null;
  currentCalculatorState: boolean = false;
  unknownWarningErrorMsg: string;
  currentAttemptId: number;
  isQuestionnaire: boolean = false;
  customConfirmTestDialogData: ICustomConfirmTestDialogData;
  interval;
  asmtFmrk;
  defaultZoom:number;
  sessions = [ // todo: we should not be depending on this default...
    {
      id: 0,
      label: 'g9_session_a',
      letter: 'A',
      sections_allowed: [0,1,2]
    }
  ]
  currentSession = this.sessions[0];
  currentSubSession: ISubSessionAttemptDef;
  ssAttemptSlugMap: ISsAttemptSlugMap = {};
  activeSubSessionId:number; 
  isText2SpeechEnabled:boolean;
  isMulTableEnabled: boolean;
  isDurationEnforced: boolean;
  sessionTabHash:string;
  isDictionaryEnabled: boolean;
  isSebMode: boolean = false;
  
  screenFreeze: boolean = false;
  asmtSlug: ASSESSMENT;
  abed_submitted: boolean = false;

  errorMessage: string;
  remainingTime: string = '999'
  timeRemainingInfo?: {
    ends_on: string,
    // time_ext_m?: number,
    // duration_m?: number,
    // isNotStarted?: boolean,
    // isSessionEnded?: boolean,
  } 
  // ANSWER KEY STUFF
  testState = {
    currentSectionIndex: 0,
    currentQuestionIndex: 0,
  };
  answerKey: { [key: string]: any } = {};
  questionScores = new Map();




  ngOnInit(): void {
    this.loginGuard.deactivate();
    this.routeSub = this.route.params.subscribe(params => {
      this.testForm = null;
      this.testSessionId = params['testSessionId'];
      // this.styleProfile.setStyleProfile("gr9styleprofile.json");

      this.userSub = this.auth.user().subscribe(async (userInfo: any) => {
        if (userInfo) {
          this.userInfo = userInfo
          this.testTakerName = [userInfo.first_name, userInfo.last_name].join(' ')
          await this.initSsAttemptSlugMap();
          this.loadQuestions();
        }
      })

      if (this.whitelabel.getSiteFlag('IS_STU_SLUG_ROUTING')){
        // todo: we should just get this with the authentication (we have the session slug at that point)
        this.auth.apiGet('public/student/session-slug', this.testSessionId).then(data => {
          this.testSessionSlug = data.slug.split('_')[1];
          if (data.slug.split('_')[0] === 'FSA') {
            const code = this.testSessionSlug.split('-')[0];
            const component = code.startsWith('LT') ? 'Literacy' : 'Numeracy';
            const grade = code.includes('4') ? 4 : 7;
            this.componentName = `Grade ${grade} ${component}`;
          } else {
            this.componentName = this.testSessionSlug;
          }
        })
      }

    })

    this.queryParamSub = this.route.queryParams.subscribe(queryParams => {
      if (queryParams['defaultZoom']){
        this.defaultZoom = +queryParams['defaultZoom'];
      }
    })
    this.studentSoftLockScreenFreezeSub = this.studentSoftLock.screenFreezeSub.subscribe((screenFreeze)=>{
      this.screenFreeze = screenFreeze;
      //console.log("Screen Freeze")
    })
    this.studentConnScreenFreezeSub = this.studentG9Connection.screenFreezeSub.subscribe((screenFreeze)=>{
      this.screenFreeze = screenFreeze;
    })
  }

  initInterval() {
    setInterval(() => {
      this.auth.apiPatch(
        this.routes.STUDENT_SESSION, 
        this.testSessionId, 
        { 
          test_attempt_id: this.testAttemptInfo.attemptId ,
          sessionTabHash: this.sessionTabHash,
        }, this.configureQueryParams())
    }, 5*60 * 1000)
  }

  configureQueryParams() {
    if (this.userInfo) {
      return {
        query: {
          schl_class_group_id: this.userInfo.sch_class_group_id
        }
      }
    }
    return null;
  }

  ngOnDestroy() {
    if (this.routeSub) {     this.routeSub.unsubscribe(); }
    if(this.queryParamSub) { this.queryParamSub.unsubscribe(); }
    if(this.userSub) {       this.userSub.unsubscribe(); }
    if(this.studentSoftLockScreenFreezeSub) { this.studentSoftLockScreenFreezeSub.unsubscribe(); }
    if(this.studentConnScreenFreezeSub){ this.studentConnScreenFreezeSub.unsubscribe();}
    clearInterval(this.interval)
    this.studentSoftLock.destroy();
    
  }

  async initSsAttemptSlugMap() {
    const res = await this.auth.apiGet(this.routes.STUDENT_CURRENT_SUBSESSION_INFO, this.testSessionId);
    //TODO: check the linear flag on test attempts to set linear.
    const subSessions = res;
    for(const def of subSessions) {
      if(def.active_sub_session_id != null) {
        this.activeSubSessionId = def.active_sub_session_id;
      }
      this.ssAttemptSlugMap[def.slug] = def;
    }
  }

  loadQuestions() {
    this.dash
      .loadAttempt(this.testSessionId)
      .then(res => {
        const payload = res[0]
        const attemptId = payload.attemptId;
        const studentLastNameInput = this.auth.getCookie("studentLastNameInput");
        const isStudentVerified = this.auth.getCookie("isStudentLastNameVerified");
        if(!isStudentVerified) {
          this.auth.apiCreate(this.routes.VERIFY_STUDENT, {studentLastNameInput, attemptId}, this.configureQueryParams())
          this.auth.setCookie("isStudentLastNameVerified", '1', (1/12)) // expire in 2 hrs
        }

        // ensure packed style profile from s3 is loaded before question display
        if(payload.styleProfileConfig){
          const {id, slug, config, version_id} = payload.styleProfileConfig
          this.styleProfile.setStyleProfileFromS3(config, slug, id, version_id)
        }
        this.isDurationEnforced = payload.isDurationEnforced;
        this.asmtSlug = payload.asmt_slug;
        this.currentAttemptId = payload.attemptId;
        this.testAttemptInfo = payload;
        this.isDictionaryEnabled = payload.isDictionaryEnabled;
        this.isQuestionnaire = payload.attempt_twtdar_order === 1;
        this.timeRemainingInfo = {
          ends_on: payload.ends_on
        }
        this.recomputeTimeRemaining()
        this.customConfirmTestDialogData = this.getCustomConfirmTestDialogData();
        // console.log('loadAttempt', payload)
        const testDesign = payload.testDesign;
        this.asmtFmrk = payload.framework ? JSON.parse(payload.framework) : {};
        this.sessionTabHash = payload.sessionTabHash;
        this.isSebMode = payload.isSebMode
        this.isText2SpeechEnabled = this.checkTTSAccomm(payload.accommodations);
        this.isMulTableEnabled = this.checkMultTableAccomm(payload.accommodations);
        this.questions = objectToNumMap(testDesign.questionDb),
          this.testForm = {
            currentTestDesign: {
              ...testDesign,
              questionDb: null,
            },
            questionStates: payload.questionStates,
            testLang: testDesign.lang,
            questionSrcDb: this.questions,
          };
        this.initInterval()
        this.studentSoftLock.init({
          uid: this.auth.getUid(),
          test_attempt_id: +attemptId,
          test_session_id: +this.testSessionId,
          sessionTabHash: payload.sessionTabHash,
          isSebMode: payload.isSebMode,
          is_soft_lock_disabled: payload.is_soft_lock_disabled,
          // is_soft_lock_enabled: payload.is_soft_lock_enabled,
          currentStudentPosition: this.currentStudentPosition, // critical that the object pointer is not broken on this
        })
        for (const slug in this.ssAttemptSlugMap) {
          if (this.ssAttemptSlugMap[slug].id === this.activeSubSessionId) {
            this.currentSubSession = this.ssAttemptSlugMap[slug];
          }
        }
        let sections_allowed = payload.sections_allowed;
        if (this.whitelabel.getSiteFlag('IS_INVIG_SECTIONS_ALLOWED_DISABLED')){
          sections_allowed = []
          for (let i=0; i<testDesign.sections.length; i++){
            sections_allowed.push(i)
          }
          payload.sections_allowed = sections_allowed
        }
        if (sections_allowed && sections_allowed[0] > 0){
          // this indicates that we are no longer in Session A
          this.currentSession = this.sessions[1]
        }
        if (payload.assistiveTech) {
          this.assisTech.setStudentAssistiveTechStatus(true);
        }
        if (payload.isPreviouslyOpened){
          // todo: trigger soft lock
          this.studentSoftLock.pauseAndLockStudent({
            isForced: true,
            attemptId: +attemptId,
          });
        }
      })
      .catch(e => {
        const { mode, isUnknown } = parseAttemptLoadErrorMessage(e.message);
        if (isUnknown) {
          this.unknownWarningErrorMsg = e.message;
        }
        else {
          this.unknownWarningErrorMsg = ''
        }
        this.errorMessage = this.lang.tra(mode);
      })
  }

  setStudentPosition($event) {
    this.currentStudentPosition.stageIndex = $event.stageIndex
    this.currentStudentPosition.questionCaption = $event.questionCaption
    if(this.studentG9Connection) {
      this.studentG9Connection.updateStudentPosition(this.currentStudentPosition);
    }    
  }

  recomputeTimeRemaining(){
    try {
      console.log('Student recomputeTimeRemaining - timeRemainingInfo', JSON.stringify(this.timeRemainingInfo, null, 2));
      const mEnd = mtz(this.timeRemainingInfo.ends_on, true);
      const mNow = mtz();
      const remaining_m = mEnd.diff(mNow, 'minutes');
      console.log('Student recomputeTimeRemaining - ends_on', this.timeRemainingInfo.ends_on);
      console.log('Student recomputeTimeRemaining - mEnd formatted', mEnd.format());
      console.log('Student recomputeTimeRemaining - mNow formatted', mNow.format());
      console.log('Student recomputeTimeRemaining - remaining_m', remaining_m);
      const h = Math.floor(remaining_m / 60);
      const m = Math.round(remaining_m - h*60);
      if (h < 0 || m < 0){
        this.remainingTime = '0m'
      }
      else {
        this.remainingTime = `${m}m`
        if (h > 0){
          this.remainingTime = `${h}h : ${this.remainingTime}`
        }
      }
      console.log('Student recomputeTimeRemaining - final remainingTime', this.remainingTime);
    }
    catch (e){
      console.error('Student recomputeTimeRemaining - error', e);
    }
  }
  async refreshTimeRemaining(){
    this.timeRemainingInfo = await this.auth.apiGet('public/student/session-time', this.currentAttemptId);
    this.recomputeTimeRemaining()
  }

  private getCustomConfirmTestDialogData(): ICustomConfirmTestDialogData {
    return this.isQuestionnaire ? {
      text: 'tr_questionnaire_submit',
      confirmMsg: 'lbl_yes',
      cancelMsg: 'lbl_no'
    } : null;
  }

  isFrameworkTimer(){
    if (this.whitelabel.getSiteFlag('IS_TIMER_ENABLED')){
      return !this.testForm.currentTestDesign.isTimerDisabled
    }
    return false;
  }

  checkTTSAccomm(accommodations:any){
    const ttsSettings:string[] = (this.whitelabel.getSiteText('STU_ACCOMM_TTS_SLUGS') || '').split(',').filter(str => !!str)
    for (let paramName of ttsSettings){
      if (accommodations[paramName]  == '1'){
        return true
      }
    }
    return false;
  }

  checkMultTableAccomm(accommodations:any){
    const multTableSettings:string[] = (this.whitelabel.getSiteText('STU_ACCOMM_MULT_TABLE_SLUGS') || '').split(',').filter(str => !!str)
    for (let paramName of multTableSettings){
      if (accommodations[paramName]  == '1'){
        return true
      }
    }
    return false;
  }


  getQuestionById(id: number) {
    let question: IQuestionConfig;
    this.questions.forEach(_question => {
      if (_question.id === id) {
        question = _question;
      }
    });
    return question;
  }

  getQuestionByLabel(label: string) {
    let question: IQuestionConfig;
    this.questions.forEach(_question => {
      if (_question.label === label) {
        question = _question;
      }
    });
    return question;
  }


  public saveQuestionResponse = async (data: any) => {
    // console.log('save question response', data)
    const clearBeforeSave = await this.studentSoftLock.beforeSaveCheck();
    if(clearBeforeSave == false){
      return;
    }
    return this.auth.apiCreate(
      this.routes.STUDENT_SESSION_QUESTION,
      {
        ...data,
        sessionTabHash: this.sessionTabHash,
        test_attempt_id: this.currentAttemptId,
      },
      this.configureQueryParams()
    );
  }

  public savePosition = async (data: {section_index, question_index, question_caption, module_id?}) => {
    const clearBeforeSave = await this.studentSoftLock.beforeSaveCheck();
    if(clearBeforeSave == false){
      return;
    }
    return this.auth.apiPatch(
      this.routes.STUDENT_ATTEMPT,
      this.currentAttemptId,
      {
        ... data,
        sessionTabHash: this.sessionTabHash,
      },
      this.configureQueryParams()
    )
  }

  public postSubmit = () => {
    if (this.asmtSlug === ASSESSMENT.G9_SAMPLE){
      this.showAnswers();
    }
    else {
      this.router.navigate([`${this.lang.c()}/student/dashboard/main`]);
    }
    return Promise.resolve();
  }

  private logSubSessionError(){
    this.auth.apiCreate(this.routes.LOG, {
      slug: 'STUDENT_SUBMIT_ERR_MISSING_SURR_SS',
      data: {
        uid: this.auth.getUid(),
        testSessionId: this.testSessionId,
        asmtLabel: this.asmtLabel,
        lang: this.lang.c(),
      }
    })
  }

  public submitTest = (skipPost?: boolean) => {

    //const subsession_slug = this.currentSession.label; 
    //const subsession_order = this.currentSession.id;
    let subsession_slug = '' 
    let subsession_order = -1
    try { 
      subsession_slug = this.currentSubSession.slug 
      subsession_order = this.currentSubSession.order
    }
    catch (e){
      this.logSubSessionError()
    }

    return this.auth.apiPatch(
      this.routes.STUDENT_SESSION_QUESTION, // bad name ... to do
      1,
      { 
        test_attempt_id: <any>this.currentAttemptId, 
        subsession_slug, 
        subsession_order,
        sessionTabHash: this.sessionTabHash,
      },
      this.configureQueryParams()
    ).then(r => {
      this.logTestSubmit();
      // after submit, stop softlock
      this.screenUnfreeze();
      document.removeEventListener('fullscreenchange', this.studentSoftLock.detectFullScreen);
      this.studentSoftLock.destroy();
      this.studentG9Connection.updateStudentPosition({submitConfig: {submitted: true, subSessionIndex: this.testAttemptInfo.subsession_index}});
      // if(this.isABED()) this.abed_submitted = true;
      // this.isShowingResults = true;
      // this.router.navigate([`${this.lang.c()}/student/dashboard/main`]);
      // if (this.testForm.currentTestDesign.sections.length == 2) {
      //   this.showAnswers();
      // }
      // else{
      //   this.router.navigate([`${this.lang.c()}/student/dashboard/main`]);
      // }
      if(!skipPost) {
        this.postSubmit();
      }
    });
    // .then( r => {
    //   this.my.gotoDashboard();
    // });
  }
  screenUnfreeze(){
    this.screenFreeze = false;
  }
  logTestSubmit() {

    // if(!this.auth.userIsStudent()) {
    //   return;
    // }

    this.auth.apiCreate(this.routes.LOG, {
      slug: 'STUDENT_TEST_SUBMIT',
      data: {
        uid: this.auth.getUid(),
        testSessionId: this.testSessionId,
        asmtLabel: this.asmtLabel,
        lang: this.lang.c(),
        states: this.testForm.questionStates,
        questions: this.testForm.questionSrcDb,
        withTeacher: this.route.snapshot.queryParams.withTeacher,
        sessionTabHash: this.sessionTabHash,
      }
    })
  }

  showAnswers = () => {
    try {
      this.auth.apiCreate(this.routes.LOG, {
        slug: 'G9_SAMPLE_TEST_SUBMIT',
        data: {
          testSessionId: this.testSessionId, // to do: bring back item set
          asmtLabel: this.asmtLabel,
          lang: this.lang.c(),
          states: this.testForm.questionStates,
          withTeacher: this.route.snapshot.queryParams.withTeacher,
          uid: this.auth.getUid(),
          sessionTabHash: this.sessionTabHash,
        }
      })
    } catch (e) { }


    try {
      this.scoreAllQuestions();
      this.feedAnswersToAllQuestions();
    } catch (e) { }
    this.selectSectionAndQuestion(0, 0);
    this.isShowingResults = true;
    // alert('The answer key page is currently not available.')
  }

  selectSectionAndQuestion(sectionIndex, questionIndex) {
    this.testState.currentSectionIndex = sectionIndex;
    this.testState.currentQuestionIndex = questionIndex;
  }
  feedAnswersToAllQuestions() {
    this.testForm.currentTestDesign.sections.forEach(section => {
      section.questions.forEach(qId => {
        const qAns = this.answerKey[qId];
        const q = this.getQuestionById(qId);
        // to do : this is fixed to MCQ
        Object.keys(qAns).forEach(entryId => {
          if (entryId !== '__meta') {
            const eAns = qAns[entryId];
            // to do: assuming flat question structures
            q.content.forEach((el: IContentElementMcq) => {
              if (el.entryId === +entryId) {
                el.options.forEach((option, optionIndex) => {
                  if (eAns.optionIndex === optionIndex) {
                    option.isCorrect = true;
                  }
                });
              }
            });
          }
        });
      });
    });
  }
  private isEResCorrect(eRes, eAns) {
    if (eRes && eRes.selections) {
      if (eRes.selections[0] && eRes.selections[0].i === eAns.optionIndex) {
        return true;
      }
    }
    return false;
  }
  scoreAllQuestions() {
    const states = this.testForm.questionStates;
    this.testForm.currentTestDesign.sections.forEach(section => {
      section.questions.forEach(qId => {
        const qAns = this.answerKey[qId];
        const qRes = states[qId];
        let isAllCorrect = true;
        if (!qRes) {
          isAllCorrect = false;
        } else {
          Object.keys(qAns).forEach(entryId => {
            const eAns = qAns[entryId];
            const eRes = qRes[entryId];
            isAllCorrect = isAllCorrect && this.isEResCorrect(eRes, eAns);
          });
        }
        const isCorrect = isAllCorrect;
        this.questionScores.set(qId, isCorrect);
      });
    });
  }

  isStuShowResultsSiteFlag() {
    return this.whitelabel.getSiteFlag('IS_STU_SHOW_RESULTS');
  }

  goToDashboard() {
    this.router.navigate([`${this.lang.c()}/student/dashboard/main`]);
  }
  getDashboardRouterLink() {
    return `/${this.lang.c()}/student/dashboard/main`
  }

  reloadAttempt(): void {
    window.location.reload();
  }
}
