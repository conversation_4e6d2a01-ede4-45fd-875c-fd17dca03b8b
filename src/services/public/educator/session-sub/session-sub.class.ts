import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { getPropVals } from '../../../../util/param-sanitization';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { dbDateSetDateTime, dbDateNow } from '../../../../util/db-dates';
import moment from 'moment';
import { Knex } from 'knex';
import absenceReportService from '../../test-admin/invigilation/absence-report/absence-report.service';
import logger from '../../../../logger';
import Redis from 'ioredis';
import _ from 'lodash';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';
import { SQL_ACTIVE_SUB_SESSIONS, SQL_CLASS_GUEST_STUDENTS, SQL_CLASS_STUDENTS, SQL_COMPLETED_SUB_SESSIONS, SQL_EXISTING_UNLOCKED_ATTEMPT, SQL_STUDENT_SUB_SESSIONS, SQL_STU_RECORDS, SQL_SUB_SESSION_PRESETS, SQL_SUB_SESSION_SLUG, SQL_TASS_STUDENTS } from './model/sql';
import { StudentSubSessionState, SubSessionDef, SubSessionDefRecord, ISubSessionPartial, SubSessionRecord, SubSessionsInfo } from './model/types';
import { SQL_CURR_ATTEMPTS, SQL_CURR_TASS, SQL_PREV_ATTEMPTS, SQL_PREV_TASS } from '../session/model/sql';

const DEFAULT_SESSION_DURATION_HOURS = 4; // todo:DB_DATA_MODEL pull from twtar
export const IS_BCED_CONTEXT = false; // todo:WHITELABEL should be more centralized
let STANDARD_TIMEZONE = 'America/Toronto'; // todo:DB_DATA_MODEL or from web client request

interface Data { }

interface ServiceOptions { }

export class SessionSub implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    const whiteLabel = this.app.get('whiteLabel');
    if (isABED(whiteLabel as FLAGS)) {
      STANDARD_TIMEZONE = 'America/Edmonton';
    }
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update(id: NullableId, data: ISubSessionPartial, params?: Params): Promise<Data> {
    const sub_session_id = id;
    const {
      test_session_id,
      session_rescheduled,
      pjSessionStartDate,
      pjSessionEndDate,
      asmtSlug
    } = data;

    let sessionSchedule;

    const scheduledStartStandardTimeMoment = moment.tz(data.date_time_start, STANDARD_TIMEZONE);
    const scheduledStartUTCTimeMoment = scheduledStartStandardTimeMoment.utc()
    const scheduledStartUTCTime = scheduledStartUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
    const scheduledEndStandardTimeMoment = moment.tz(data.date_time_end, STANDARD_TIMEZONE);
    const scheduledEndUTCTimeMoment = scheduledEndStandardTimeMoment.utc()
    const scheduledEndUTCTime = scheduledEndUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
    const scheduledSessionStartStandardTimeMoment = moment.tz(pjSessionStartDate, STANDARD_TIMEZONE);
    const scheduledSessionStartUTCTimeMoment = scheduledSessionStartStandardTimeMoment.utc()
    const scheduledSessionStartUTCTime = scheduledSessionStartUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
    const scheduledSessionEndStandardTimeMoment = moment.tz(pjSessionEndDate, STANDARD_TIMEZONE);
    const scheduledSessionEndUTCTimeMoment = scheduledSessionEndStandardTimeMoment.utc()
    const scheduledSessionEndUTCTime = scheduledSessionEndUTCTimeMoment.format('YYYY-MM-DDTHH:mm');

    //const datetime_start = dbDateSetDateTime(this.app, 4 ,data.date_time_start)
    const datetime_start = dbDateSetDateTime(this.app, 0 , scheduledStartUTCTime)
    const datetime_end = dbDateSetDateTime(this.app, 0 , scheduledEndUTCTime)

    //check if the start end time is within test window start end time
    const test_windows = await this.getData([test_session_id] , `
        select tw.*
          from test_sessions ts
          join test_windows tw on tw.id = ts.test_window_id
         where ts.id = ?
    ;`);
    if(test_windows.length ==  0){
      throw new Errors.BadRequest("TEST_WINDOW_NOT_FOUND");
    }

    const test_window = test_windows[0]
    const tw_start = dbDateSetDateTime(this.app, 0 , test_window.date_start)
    const tw_end = dbDateSetDateTime(this.app, 0 , test_window.date_end)

    if(datetime_start < tw_start || datetime_start > tw_end || datetime_end < tw_start || datetime_end > tw_end) {
      throw new Errors.BadRequest("INVALID_TEST_SESSION_TIME");
    }


    if(session_rescheduled){
      //const date_time_start = dbDateSetDateTime(this.app, 4 ,data.date_time_start)
      let date_time_start = dbDateSetDateTime(this.app, 0, scheduledStartUTCTime);
      let date_time_end;
      if(asmtSlug == 'PRIMARY_SAMPLE' || asmtSlug == 'PRIMARY_OPERATIONAL' || asmtSlug == 'JUNIOR_SAMPLE' || asmtSlug == 'JUNIOR_OPERATIONAL'){
        date_time_start = dbDateSetDateTime(this.app, 0, scheduledSessionStartUTCTime);
        date_time_end = dbDateSetDateTime(this.app, 0, scheduledSessionEndUTCTime);
      }
      sessionSchedule = await this.app.service('db/write/test-sessions').patch(test_session_id, {date_time_start, date_time_end});
    }
    const subsessionRecord = await this.app.service('db/write/test-session-sub-sessions').patch(sub_session_id,{datetime_start, datetime_end});

    return {
      sessionSchedule,
      subsessionRecord,
    }
  }

  async patch(id: NullableId, data: { subSessionId: number, twtdarOrder: number, classId: number, isClosing: boolean, studentUids: number[], isPJ:boolean}, params?: Params,): Promise<Data> {
    return this.patchSession(id, data, params);
  }

  // async patchSession(id: NullableId, data: { subSessionId: number, twtdarOrder: number, classId: number, isClosing: boolean, studentUids: number[] }, params?: Params): Promise<Data> {
  async patchSession(id: NullableId, data: any, params?: Params): Promise<Data> {
    const { school_class_group_id } = (<any>params).query;
    const test_session_id = id;
    let {
      subSessionId,
      twtdarOrder,
      isClosing,
      studentUids,
      classId,
      isPJ,
      isSubmitting
    } = data;
    const changed: any[] = [];
    const errors: any[] = [];

    // const subSessionSlug = await dbRawRead(this.app, {test_session_id, studentUids, subSessionId}, SQL_SUB_SESSION_SLUG);
    if(!isClosing) {
      //i needed to bring back the scts query since i need the slug of the assessment.
      const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
      const sctsRecord = sctsRecords[0];
      const slug = sctsRecord.slug;
      const startedSomewhereElse = await dbRawWrite(this.app, {slug, studentUids}, SQL_EXISTING_UNLOCKED_ATTEMPT);
      //if any student is active in another class that is running the same operational assessment, then we include them in the errors
      if(startedSomewhereElse.length){
        errors.push({message: "AlreadyHasPermission", students: startedSomewhereElse});
        //we remove the students who already got permission from the student list to be process since they should not get unlocked.
        studentUids = studentUids.filter((uid: any) => startedSomewhereElse.findIndex(started => started.uid == uid) == -1);
      }
      if(!studentUids.length){
        //if there are no more students to be unlocked then end the process
        return { changed, errors };
      }
    }
    const testAttempts = <any[]>await this.app
    .service('db/read/test-attempt-sub-sessions')
    .find({
      query: {
        test_session_id,
        uid: { $in: studentUids },
        sub_session_id: subSessionId,
        is_invalid: {$ne: 1}
        // twtdar_order: twtdarOrder
      },
      paginate: false,
    });

    let attemptsToClose = [];

    if(!isClosing) { //can only have one session active at a time.
      attemptsToClose = <any[]>await this.app
      .service('db/read/test-attempts')
      .find({
        query: {
          test_session_id,
          uid: { $in: studentUids },
          active_sub_session_id: {$ne: null}
        },
        paginate: false,
      });
    }
    
    const testAttemptIds = testAttempts.map(entry => entry.test_attempt_id);
    const testAttemptIdsToClose = isClosing ? testAttemptIds : attemptsToClose.map(entry => entry.id);
    if (testAttemptIds.length > 0) {
      if (testAttemptIdsToClose.length) {
        await dbRawWrite(this.app, [testAttemptIdsToClose], `
          UPDATE test_attempts
          SET active_sub_session_id = NULL
          WHERE id IN (?)
        ;`);
        await dbRawWrite(this.app, [testAttemptIdsToClose, subSessionId], `
          UPDATE test_attempt_sub_sessions
          SET last_locked_on = NOW()
          WHERE test_attempt_id IN (?)
            AND sub_session_id = ?
            AND is_invalid != 1
          ;`);

        if (isSubmitting) {
          await dbRawWrite(this.app, [testAttemptIds, subSessionId], `
            UPDATE test_attempt_sub_sessions
            SET is_submitted = 1
            WHERE test_attempt_id IN (?)
              AND sub_session_id = ?
              AND is_invalid != 1
          ;`);
        }

      }

      if(!isClosing) {
        await dbRawWrite(this.app, [subSessionId, testAttemptIds], `
          UPDATE test_attempts
          SET active_sub_session_id = ?
          WHERE id IN (?)
        ;`);
      }
    }
    return { changed, errors };
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
  private async checkActiveSubSessions(studentUids: number[]) {
    const active_sessions = await dbRawRead(this.app, 
      {studentUids}, 
      SQL_ACTIVE_SUB_SESSIONS
    );
    return (active_sessions.length > 0);
  }
  private async checkCompletedSubSessions(studentUids: number[], subSessionId: number, classId: number) {
    const active_sub_session = await this.app.service('db/read/test-session-sub-sessions').get(subSessionId);
    const session_slug = active_sub_session.slug;
    const completed_sessions = await dbRawRead(this.app, 
      {classId, studentUids}, 
      SQL_COMPLETED_SUB_SESSIONS
    );
    const is_session_completed = completed_sessions.filter(session => { return session.sub_session_slug === session_slug })
    return (is_session_completed.length > 0);
  }

  private async getData(props: any[], query: string) {
    const db = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]>res[0];
  }

  async getStudentsInSessionClass(school_class_id: number, isForced?:boolean) {
    // sqlMessage:"Unknown column 'schl_student' in 'on clause'"
    const studentRecords = <any[]>await dbRawRead(this.app, 
      {school_class_id}, 
      SQL_CLASS_STUDENTS(isForced)
    );

    const guestClassStudentRecord = <any[]>await dbRawRead(this.app, 
      {school_class_id}, 
      SQL_CLASS_GUEST_STUDENTS(isForced)
    );
    const returnRecords = studentRecords.concat(guestClassStudentRecord)
    return returnRecords.map(r => r.uid);
  }

  unlockStudentSubSessions() {

  }

  // ensures intialization
  async getSubSessions(test_session_id: Id, isForced?:boolean):Promise<SubSessionsInfo | undefined> {
    if(!test_session_id) {
      throw new Errors.BadRequest();
    }
    const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    const sctsRecord = sctsRecords[0];
    const school_class_id = sctsRecord.school_class_id;
    const subSessionRecords_raw = await this.getSubSessionRecords(test_session_id);
    const subSessionOrderIndex = new Map();
    const subSessionRecords:SubSessionRecord[] = _.orderBy(subSessionRecords_raw, 'order').map((entry: any) => Object({
      id: entry.id,
      order: entry.order,
      slug: entry.slug,
      caption: entry.caption,
      date_time_start:entry.datetime_start,
      duration_hours:entry.duration_hours,
      twtdar_order: entry.twtdar_order,
      is_last: entry.is_last
    }))

    //Get the slug from one of the subsession records
    subSessionRecords.forEach((subSessionRecord, i) => {
      subSessionOrderIndex.set(subSessionRecord.id, i)
    })
    // test takers
    const studentUids = await this.getStudentsInSessionClass(school_class_id, isForced);
    let studentRecords;
    if(studentUids.length>0) {
     studentRecords = <any[]>await dbRawRead(
      this.app, 
      {test_session_id, studentUids}, 
      SQL_STU_RECORDS);
    }
    if (studentRecords) {
      const uidToTestAttemptPromise = new Map<number, Map<number, Promise<any>>>();

      const getCreateTestAttemptPromise = (uid: number, twtdar_order: number) => {
        const twtdarOrderToTestAttemptPromise = uidToTestAttemptPromise.get(uid);
        if (twtdarOrderToTestAttemptPromise) {
          return twtdarOrderToTestAttemptPromise.get(twtdar_order);
        }
      }

      const setCreatedTestAttemptPromise = (uid: number, twtdar_order: number, taCreatePromise: Promise<any>) => {
        let twtdarOrderToTestAttempt = uidToTestAttemptPromise.get(uid);
        if (!twtdarOrderToTestAttempt) {
          twtdarOrderToTestAttempt = new Map<number, Promise<any>>();
        }
        twtdarOrderToTestAttempt.set(twtdar_order, taCreatePromise);
        uidToTestAttemptPromise.set(uid, twtdarOrderToTestAttempt);
      }

      const sessionRecord = (await dbRawRead(this.app, [test_session_id], `
        select TIMESTAMPDIFF(SECOND, is_students_initing, now()) time_since
             , is_students_initing
             , test_window_id
        from test_sessions
        where id = ?
      ;`))[0];
      if(sessionRecord.is_students_initing && sessionRecord.time_since < 30) {
        // throw new Errors.Forbidden('SESSION_CREATION_IN_PROCESS');
      }
      await this.app.service('db/write/test-sessions').patch(test_session_id, {is_students_initing: dbDateNow(this.app)});

      const test_window_id = sessionRecord.test_window_id;
      let maxtwtdar_order = 0
      subSessionRecords.forEach( (sr:any) => {if (sr.twtdar_order > maxtwtdar_order) maxtwtdar_order = sr.twtdar_order })
      let studentRecordInitError:any;
      try {
        await Promise.all(studentRecords.map(async studentRecord => {
          //if (studentRecord.tass_id == null) {  // comment out so if the student(mostly guest student) write on other class session and return it will use the attempt wrote in other session instead current one.
            //for (let subSessionRecord_i=0; subSessionRecord_i<subSessionRecords.length; subSessionRecord_i++){
            for(let twtdar_order = 0; twtdar_order < maxtwtdar_order+1; twtdar_order++){
              const subSessionRecord = subSessionRecords.find(sr => sr.twtdar_order == twtdar_order);
              const uid = studentRecord.uid;
              //const twtdar_order = subSessionRecord.twtdar_order;
              const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
              const sctsRecord = sctsRecords[0];
              const slug = sctsRecord.slug;
              const previousAttemptsRecords = <any[]>await dbRawRead(this.app, 
                {test_window_id, uid, twtdar_order, slug}, 
                SQL_PREV_ATTEMPTS
              );

              const currentAttemptsRecords = <any[]>await dbRawRead(this.app, 
                {test_session_id, uid, twtdar_order}, 
                SQL_CURR_ATTEMPTS
              );

              const currentTestAttemptIds =  currentAttemptsRecords.map(attempt => attempt.id)

              let currentAttemptsSubSessionRecords = [];
              if(currentTestAttemptIds.length > 0){
                currentAttemptsSubSessionRecords = <any[]>await dbRawRead(this.app, 
                  {currentTestAttemptIds}, 
                  SQL_CURR_TASS
                );
              }

              let lastTestAttempt;
              let previousAttemptId
              let isPreviousAttempt = false;
              let test_attempt_id;

              const whiteLabel = this.app.get('whiteLabel');
              if (previousAttemptsRecords.length > 0){
                let taqrRecord :any[] = []
                for(let prev_test_attempt_i= 0; prev_test_attempt_i<previousAttemptsRecords.length; prev_test_attempt_i++){
                  const this_lastTestAttempt = previousAttemptsRecords[prev_test_attempt_i]
                  const this_previousAttemptId = this_lastTestAttempt.id;
                  const this_taqrRecord = <any[]>await this.app
                  .service('db/read/test-attempt-question-responses')
                  .find({
                    query: {
                      test_attempt_id:this_previousAttemptId,
                    },
                    paginate: false,
                  });
                  if(this_taqrRecord.length > taqrRecord.length){
                    lastTestAttempt = this_lastTestAttempt
                    previousAttemptId = this_previousAttemptId
                    taqrRecord = this_taqrRecord
                  }
                }

                const startedSomewhereElse = await dbRawWrite(this.app, {slug, studentUids: uid}, SQL_EXISTING_UNLOCKED_ATTEMPT);
                //skip the current student's initilization if they are unlocked in another operational assessment
                if(startedSomewhereElse.length){
                  continue;
                }
                if(taqrRecord.length > 0 && lastTestAttempt && +lastTestAttempt.test_session_id != +test_session_id){
                  //revoke the current test_attempt
                  await Promise.all(currentAttemptsRecords.map(async ta =>{
                    await this.app
                      .service('db/write/test-attempts')
                      .patch(ta.id,{
                        uid: 0-(+ta.uid),
                        test_session_id: 0 - (ta.test_session_id),
                        is_invalid: 1
                      });
                  }))
                  //revoke the current test_attempt_subsession
                  await Promise.all(currentAttemptsSubSessionRecords.map(async tass =>{
                    await this.app
                      .service('db/write/test-attempt-sub-sessions')
                      .patch(tass.id,{
                        uid: 0-(+tass.uid),
                        test_session_id: 0 - (tass.test_session_id),
                        is_invalid : 1
                      });
                  }))

                  const testSessionSubSessions = <any[]>await this.app
                    .service('db/read/test-session-sub-sessions')
                    .find({
                      query: {
                        test_session_id,
                      },
                      paginate: false,
                    });

                  const previousAttemptsSubSessionRecords = <any[]>await dbRawRead(this.app, 
                    {previousAttemptId}, 
                    SQL_PREV_TASS
                  );

                  const testAttempts = <any[]>await this.app
                    .service('db/write/test-attempts')
                    .patch(lastTestAttempt.id,{test_session_id});

                  for(let record of previousAttemptsSubSessionRecords){
                    const testSessionSubSession = testSessionSubSessions.find( subSession =>subSession.slug == record.slug )
                    const sub_session_id = testSessionSubSession.id;
                    await this.app.service('db/write/test-attempt-sub-sessions')
                      .patch(record.id,{test_session_id, sub_session_id});
                  }

                  test_attempt_id = previousAttemptId;
                  isPreviousAttempt = true;
                }
              }

              //test atttempt id should be link only if there is a corrent SubSession
              if(!test_attempt_id && currentTestAttemptIds.length > 0 && currentAttemptsSubSessionRecords.length > 0 && subSessionRecord){
                const currentAttemptsSubSessionRecord = currentAttemptsSubSessionRecords.find( tass => tass.sub_session_id == subSessionRecord.id)
                if(currentAttemptsSubSessionRecord){
                  test_attempt_id = currentTestAttemptIds[0]
                }
              }
              //test_attempt_id = studentRecord.test_attempt_id;

              let attempt;
              if (!IS_BCED_CONTEXT){
                if (!test_attempt_id && !isPreviousAttempt) {
                  const taCreatePromise = getCreateTestAttemptPromise(uid, twtdar_order);
                  if (taCreatePromise) {
                    attempt = await taCreatePromise;
                    test_attempt_id = attempt.id;
                  } else {
                    const taCreatePromise = await this.app
                      .service('public/student/session')
                      .createAttemptByTeacher(uid, <number>test_session_id, twtdar_order);
                    setCreatedTestAttemptPromise(uid, twtdar_order, taCreatePromise);
                    attempt = await taCreatePromise;
                    test_attempt_id = attempt.id

                    //create test attempt sub session
                    const twdarOrderSubSessionRecords = subSessionRecords.filter( ssr => ssr.twtdar_order == twtdar_order )
                    for (let subSessionRecord_i=0; subSessionRecord_i<twdarOrderSubSessionRecords.length; subSessionRecord_i++){
                      const subSessionRecord = twdarOrderSubSessionRecords[subSessionRecord_i];
                      const sections_allowed = this.getSectionsAllowed(attempt?.subsession_meta, subSessionRecord.slug)

                      const sub_session_id = subSessionRecord.id;
                      await this.app.service('db/write/test-attempt-sub-sessions').create({
                        test_session_id,
                        sub_session_id,
                        test_attempt_id:test_attempt_id || previousAttemptId,
                        uid,
                        num_responses: 0,
                        sections_allowed
                      });
                    }
                  }

                  //check if there's more than 1 test attempts get created by other concurrent API call
                  const existingTestAttempts = <any[]>await dbRawWrite(this.app, [test_session_id, uid, twtdar_order], `
                    select ta.*
                      from test_attempts ta
                    where ta.test_session_id = ?
                      and ta.uid = ?
                      and ta.twtdar_order = ?
                      and ta.is_invalid = 0
                    order by ta.id asc
                  ;`);

                  //unlink the extra test attempts, just keep existingTestAttempts[0]
                  if(existingTestAttempts.length > 1){
                    for (let ta_i = 1; ta_i<existingTestAttempts.length; ta_i++ ){
                      const test_attempt_id = existingTestAttempts[ta_i].id
                      const new_uid = 0 - (+uid) // -uid
                      const new_test_session_id = 0 - (+test_session_id) // -test_session_id
                      await this.app.service('db/write/test-attempts').patch(test_attempt_id, {
                        uid: new_uid,
                        test_session_id: new_test_session_id,
                        is_invalid : 1
                      });

                      const  existingAttemptsSubSessionRecords = <any[]>await dbRawWrite(this.app, [test_attempt_id], `
                          select tass.*
                            from test_attempt_sub_sessions tass
                          where tass.test_attempt_id = ?
                            and tass.is_invalid != 1
                        ;`);

                      await Promise.all(existingAttemptsSubSessionRecords.map(async etassr =>{
                        await this.app
                          .service('db/write/test-attempt-sub-sessions')
                          .patch(etassr.id,{
                            uid: 0-(+etassr.uid),
                            test_session_id: 0 - (etassr.test_session_id),
                            is_invalid : 1
                          });
                      }))
                    }
                  }
                }
              }
            }
          //}
        }));
      }
      catch (e){
        studentRecordInitError = e;
      }

      await this.app.service('db/write/test-sessions').patch(test_session_id, {is_students_initing: null});

      if (studentRecordInitError){
        throw studentRecordInitError;
      }
    }

    const db:Knex = await this.app.get('knexClientRead');

    const studentAttemptRecords = <any[]>await db('test_attempts as ta')
    .join('test_forms as tf', 'tf.id', 'ta.test_form_id')
    .leftJoin('test_attempt_unsubmissions as tau', {
      'tau.test_session_id': 'ta.test_session_id',
      'tau.student_uid': 'ta.uid',
      'tau.is_approved': 0,
      'tau.is_revoked': 0,
      'tau.is_pending': 1
    })
    .whereIn('ta.uid', studentUids)
    .where('ta.test_session_id', test_session_id)
    .whereNot('is_invalid', 1)
    .select('ta.*', 'tf.test_design_id', 'tau.is_pending as is_unsubmit_pending') // includes ta.time_ext_m

    let studentSubSessionRecords: any = [];
    if(studentUids.length>0){
      studentSubSessionRecords = await dbRawRead(
        this.app, 
        {studentUids, test_session_id},
        SQL_STUDENT_SUB_SESSIONS
      )
    }
    const studentStates: { [key: string]: StudentSubSessionState } = {};
    // const studentSubSessionStates:any[] = [];
    const ensureStudentState = (uid: number) => {
      let studentState = studentStates[uid];
      if (!studentState) {
        studentState = {
          uid,
          subSessions: []
        }
        studentStates[uid] = studentState;
      }
      return studentState;
    }
    studentAttemptRecords.forEach(attempt => {
      let studentState = ensureStudentState(attempt.uid);
      if (studentState.active_sub_session_id !== null && studentState.active_sub_session_id !== undefined) {
        return;  //We already have the attempt corresponding to the active subsession for this student
      }
      studentState.attempt_id = attempt.id;
      studentState.last_touch_on = attempt.last_touch_on;
      studentState.time_ext_m = attempt.time_ext_m;
      studentState.active_sub_session_id = attempt.active_sub_session_id;
      studentState.section_index = attempt.section_index;
      studentState.question_index = attempt.question_index;
      studentState.question_caption = attempt.question_caption;
      studentState.is_submitted = attempt.is_submitted;
      studentState.is_paused = attempt.is_paused;
      studentState.is_ta_unsubmit_pending = !!attempt.is_unsubmit_pending;
      studentState.started_on = attempt.started_on;
    })

    studentSubSessionRecords.forEach((entry: any) => {
      let studentState = ensureStudentState(entry.uid)
      const sessionIndex = subSessionOrderIndex.get(entry.sub_session_id)
      let debugSubsessions = getPropVals(entry, [
        'is_submitted',
        'num_responses',
        'started_on',
        'last_locked_on',
        'last_touch_on',
        'subtracted_time',
        'sections_allowed',
        'test_attempt_id'
      ]);

      if(debugSubsessions.sections_allowed) {
        debugSubsessions.sections_allowed = JSON.parse(debugSubsessions.sections_allowed);
      }
      debugSubsessions.tass_id = entry.id;
      debugSubsessions.subsession_slug = entry.subsession_slug;
      debugSubsessions.test_attempt_id = entry.test_attempt_id;
      studentState.subSessions[sessionIndex] = debugSubsessions;
    });

    const redis:Redis = this.app.get('redis');
    if (redis && studentAttemptRecords && studentAttemptRecords.length) {
      const cachedAttemptPos = _(await redis.mget(studentAttemptRecords.map(s => `attemptPos:${s.id}`)))
        .compact()
        .map((ap:string) => JSON.parse(ap))
        .keyBy('test_attempt_id')
        .value();

      const getSubmDataPipeline = redis.pipeline();
      studentAttemptRecords.forEach((s) => {
        getSubmDataPipeline.hgetall(`submData:${s.id}`);
      });
      const cachedSubmData = await getSubmDataPipeline.exec();
      const submDataChunk: any[] = _(cachedSubmData)
        .map((r: any) => Object.values(r[1]))
        .flatten()
        .map((r: any)=> JSON.parse(r))
        .compact()
        .value();
      const mostRecentTAQRs:any = _(submDataChunk)
        .groupBy('tass_id')
        .map((tassIdGroup:any[]) => (_.sortBy(tassIdGroup, 'timestamp').pop()))
        .keyBy('tass_id')
        .value();

      _.forEach(studentStates, (studentState) => {
        const formatTime = (t: number) => moment(t).utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        
        for(let subSession of studentState.subSessions) {
          const mostRecentTAQR = mostRecentTAQRs[subSession.tass_id];
          if(mostRecentTAQR) {
            subSession.last_touch_on = formatTime(mostRecentTAQR.timestamp);
          }
        }
        
        const cached = cachedAttemptPos[studentState.attempt_id || -1];
        if (!cached) {
          return;
        }
        const last_touch_on = formatTime(cached.last_touch_on);
        studentState.last_touch_on = last_touch_on
        studentState.section_index = cached.section_index;
        studentState.question_index = cached.question_index;
        studentState.question_caption = cached.question_caption;
        studentState.module_id = cached.module_id;
      });
    }

    return { studentStates, subSessionRecords };
  }

  getSectionsAllowed(subsession_meta:any, slug:string) {
    let subsession_meta_obj;
    let sections_allowed;
    if(subsession_meta) {
      subsession_meta_obj = JSON.parse(subsession_meta);
      sections_allowed = subsession_meta_obj[slug].sections;
      if(sections_allowed) {
        sections_allowed = JSON.stringify(sections_allowed);
      }
    }
    return sections_allowed;
  }

  async getSubSessionRecords(test_session_id: Id){
      // session as a whole
      let subSessionRecords = <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: { test_session_id }, paginate: false });
      if (subSessionRecords.length === 0) {
        subSessionRecords = await this.initSubSessionRecords(test_session_id);
      }
      return subSessionRecords
  }

  async initSubSessionRecords(test_session_id: Id, scheduled_time?:string[], is_fi=false){
    // determine who many sessions based on school session slug
    const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    const sctsRecord = sctsRecords[0];
    const slug = sctsRecord.slug as string;
    const subSessionDefs = await this.getSubSessionDefs(slug, test_session_id);
    const subSessionRecords:SubSessionRecord[] = [];
    for (let order=0; order<subSessionDefs.length; order++){
      const subSessionDef = subSessionDefs[order];
      const {datetime_start, datetime_end, duration_hours} = this.parseSubSessionDateTimeDur(slug, order, is_fi, scheduled_time);
      const record:SubSessionRecord = await this.app.service('db/write/test-session-sub-sessions').create({
        test_session_id,
        order,
        datetime_start,
        duration_hours,
        caption: subSessionDef.caption,
        slug: subSessionDef.slug,
        sections_allowed: JSON.stringify(subSessionDef.sections), //Note: deprecated, but keeping this temporarily for a transition period until it is safe to remove
        twtdar_order: subSessionDef.twtdar_order,
        is_last: subSessionDef.is_last,
        datetime_end,
      });
      subSessionRecords.push(record)
    }
    return subSessionRecords;
  }

  private parseSubSessionDateTimeDur(slug:string, order:number, is_fi:boolean, scheduled_time?:string[]){
    // todo:db_model remove all of this hard-coded logic
    let datetime_start, datetime_end;
    if ((slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') && scheduled_time && (order < scheduled_time.length)){
      const scheduledStandardTimeMoment = moment.tz(scheduled_time[order], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
      const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      datetime_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime);
    }
    else if ((slug.includes('ABED_')) && scheduled_time) {
      const scheduledStandardTimeMoment = moment.tz(scheduled_time[order], STANDARD_TIMEZONE);
      const scheduledUTCTimeMoment = scheduledStandardTimeMoment.utc()
      const scheduledUTCTime = scheduledUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      datetime_start = dbDateSetDateTime(this.app, 0, scheduledUTCTime);
    }
    else if((slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && scheduled_time) {
      let scheduledStartStandardTimeMoment = moment.tz(scheduled_time[0], STANDARD_TIMEZONE); // for PJ Sample if order == 0 or 1; for PJ Operational if order == 0 or 1 or 2 or 3
      let scheduledEndStandardTimeMoment = moment.tz(scheduled_time[1], STANDARD_TIMEZONE);   // for PJ Sample if order == 0 or 1; for PJ Operational if order == 0 or 1 or 2 or 3
      if((slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE') && (order == 2 || order == 3)) {
        scheduledStartStandardTimeMoment = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
        scheduledEndStandardTimeMoment = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
      }
      if((slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && (order == 4 || order == 5 || order == 6 || order == 7)) {
        scheduledStartStandardTimeMoment = moment.tz(scheduled_time[2], STANDARD_TIMEZONE);
        scheduledEndStandardTimeMoment = moment.tz(scheduled_time[3], STANDARD_TIMEZONE);
      }
      const scheduledStartUTCTimeMoment = scheduledStartStandardTimeMoment.utc();
      const scheduledEndUTCTimeMoment = scheduledEndStandardTimeMoment.utc();
      const scheduledStartUTCTime = scheduledStartUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      const scheduledEndUTCTime = scheduledEndUTCTimeMoment.format('YYYY-MM-DDTHH:mm');
      datetime_start = dbDateSetDateTime(this.app, 0, scheduledStartUTCTime);
      datetime_end = dbDateSetDateTime(this.app, 0, scheduledEndUTCTime);
      if((slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') && order == 8) {
        datetime_start = dbDateNow(this.app);
        datetime_end = null;
      }
    }
    else if(is_fi && slug === 'PRIMARY_SAMPLE') {
      if(order == 0 || order == 1) {
        datetime_start = null;
      }
      if(order == 2 || order == 3) {
        datetime_start = dbDateNow(this.app);
      }
    }
    else{
      datetime_start = dbDateNow(this.app);
    }
    let duration_hours;
    if(slug === 'G9_SAMPLE' || slug === 'G9_OPERATIONAL' || slug === 'OSSLT_SAMPLE' || slug === 'OSSLT_OPERATIONAL') {
      duration_hours = DEFAULT_SESSION_DURATION_HOURS; // todo: this should only come from the test window
    }
    if(slug === 'PRIMARY_SAMPLE' || slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_SAMPLE' || slug === 'JUNIOR_OPERATIONAL') {
      duration_hours = null;
    }
    return {datetime_start, datetime_end, duration_hours}
  }

  private async getSubSessionDefs(slug:string, test_session_id:Id){
    const subSessionPresets:SubSessionDefRecord[] = await dbRawRead(this.app, {slug}, SQL_SUB_SESSION_PRESETS);
    let subSessionDefs:SubSessionDef[];
    if (subSessionPresets.length > 0) {
      subSessionDefs = subSessionPresets.map((subSession) => {
        return {
          ... subSession,
          sections: JSON.parse(subSession.sections)
        }
      })
    } 
    else { // todo:deprecate else statement once all assessments are defined with sub session presets
      //Note: all subsessions with the same twtdar_order must be defined consecutively.
      const nbedSubSessionSingle =  new Set(['SCIENCES8_OPERATIONAL', 'TCN_OPERATIONAL', 'SCIENCES8_SAMPLE', 'TCN_OPERATIONAL', 'TCLE_SAMPLE']);
      if (slug === 'G9_SAMPLE') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1], twtdar_order: 0 },
        ];
      } else if (slug === 'OSSLT_SAMPLE') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1,2], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [3,4,5], twtdar_order: 0 },
        ];
      } else if (slug === 'OSSLT_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1,2], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [3,4,5], twtdar_order: 0 },
          { slug: 'session_q', caption: 'Q', sections: [0], twtdar_order: 1 }
        ];
      } else if (slug === 'G9_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0,1], twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', sections: [2,3], twtdar_order: 0 },
          { slug: 'session_q', caption: 'Q', sections: [0], twtdar_order: 1 }
        ];
      } else if (slug === 'PRIMARY_SAMPLE' || slug === 'JUNIOR_SAMPLE') {
        subSessionDefs = [
          { slug: 'lang_session_a', caption: 'A', twtdar_order: 0},
          { slug: 'lang_session_b', caption: 'B', twtdar_order: 0},
          { slug: 'math_stage_1', caption: '1', twtdar_order: 1},
          { slug: 'math_stage_2', caption: '2', twtdar_order: 1}
        ]
      } else if (slug === 'PRIMARY_OPERATIONAL' || slug === 'JUNIOR_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'lang_session_a', caption: 'A', twtdar_order: 0},
          { slug: 'lang_session_b', caption: 'B', twtdar_order: 0},
          { slug: 'lang_session_c', caption: 'C', twtdar_order: 0},
          { slug: 'lang_session_d', caption: 'D', twtdar_order: 0},
          { slug: 'math_stage_1', caption: '1', twtdar_order: 1},
          { slug: 'math_stage_2', caption: '2', twtdar_order: 1},
          { slug: 'math_stage_3', caption: '3', twtdar_order: 1},
          { slug: 'math_stage_4', caption: '4', twtdar_order: 1},
          { slug: 'session_q', caption: 'Q', twtdar_order: 2}
        ]
      } else if (slug === 'TCLE_OPERATIONAL') {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', twtdar_order: 0 },
          { slug: 'session_b', caption: 'B', twtdar_order: 1 }
        ];
      } else if (slug.startsWith('GRAD') || nbedSubSessionSingle.has(slug)) {
        const testSessionRecord = await this.app.service('db/read/test-sessions').get(test_session_id);
        const testWindowId = testSessionRecord.test_window_id;
        const allocRules = <any[]>await this.app.service('db/read/test-window-td-alloc-rules').find({
          query: {
            $select: ['test_design_id'],
            slug: slug,
            test_window_id: testWindowId,
            $limit: 1,
          },
          paginate: false,
        });
        const testDesignId = allocRules[0].test_design_id;
        const testDesign = await this.app.service('db/read/test-designs').get(testDesignId);
        const framework = JSON.parse(testDesign.framework);
        logger.silly({ frameworkPartitions: framework.partitions });
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: Array.from(Array(framework.partitions.length).keys()), twtdar_order: 0},
        ];
      } else {
        subSessionDefs = [
          { slug: 'session_a', caption: 'A', sections: [0], twtdar_order: 0 },
        ];
      }
    }

    subSessionDefs.forEach((def, index) => {
      let nextDef = undefined;
      if (index + 1 < subSessionDefs.length) {
        nextDef = subSessionDefs[index + 1];
      }
      if (!nextDef || nextDef.twtdar_order !== def.twtdar_order) {
        def.is_last = 1;
      } 
      else {
        def.is_last = 0;
      }
    });

    return subSessionDefs
  }

  async getTestSubSessions(test_session_id: Id) {
    // determine who many sessions based on school session slug
    const sctsRecords = <any[]>await this.app.service('db/read/school-class-test-sessions').find({ query: { test_session_id }, paginate: false });
    const sctsRecord = sctsRecords[0];
    const school_class_id = sctsRecord.school_class_id;
    const slug = sctsRecord.slug;
    // session as a whole
    let subSessionRecords = <any[]>await this.app.service('db/read/test-session-sub-sessions').find({ query: { test_session_id }, paginate: false });
    if (subSessionRecords.length === 0) {
      let subSessionDefs: any[] = [];

      subSessionRecords = await Promise.all(subSessionDefs.map((subSessionDef, order) => {
        const twtdar_order = subSessionDef.twtdar_order;

        return this.app.service('db/write/test-session-sub-sessions').create({
          test_session_id,
          order,
          caption: subSessionDef.caption,
          slug: subSessionDef.slug,
          twtdar_order,
          is_last: subSessionDef.is_last
        });
      }));
    }
    const subSessionOrderIndex = new Map();
    subSessionRecords = _.orderBy(subSessionRecords, 'order').map((entry: any) => Object({
      id: entry.id,
      order: entry.order,
      slug: entry.slug,
      caption: entry.caption,
      twtdar_order: entry.twtdar_order,
      is_last: entry.is_last,
      datetime_start: entry.datetime_start,
      datetime_end: entry.datetime_end,
    }))
    subSessionRecords.forEach((subSessionRecord, i) => {
      subSessionOrderIndex.set(subSessionRecord.id, i)
    })
    // test takers
    const studentUids = await this.getStudentsInSessionClass(school_class_id);

    let studentRecords;
    if(studentUids.length>0){
      studentRecords = <any[]>await dbRawRead(
        this.app, 
        {test_session_id, studentUids}, 
        SQL_TASS_STUDENTS
      );
    }
    const query = { uid: { $in: studentUids }, test_session_id, is_invalid: {$ne: 1} }
    const studentAttemptRecords = <any[]>await this.app.service('db/read/test-attempts').find({ query, paginate: false });
    const studentSubSessionRecords = <any[]>await this.app.service('db/read/test-attempt-sub-sessions').find({ query, paginate: false });
    const studentStates: { [key: string]: any } = {};
    // const studentSubSessionStates:any[] = [];
    const ensureStudentState = (uid: number) => {
      let studentState = studentStates[uid];
      if (!studentState) {
        studentState = {
          uid,
          subSessions: []
        }
        studentStates[uid] = studentState;
      }
      return studentState;
    }
    studentAttemptRecords.forEach(attempt => {
      let studentState = ensureStudentState(attempt.uid);
      if (studentState.active_sub_session_id !== null && studentState.active_sub_session_id !== undefined) {
        return;  //We already have the attempt corresponding to the active subsession for this student
      }
      studentState.attempt_id = attempt.id;
      studentState.last_touch_on = attempt.last_touch_on;
      studentState.active_sub_session_id = attempt.active_sub_session_id;
      studentState.section_index = attempt.section_index;
      studentState.question_index = attempt.question_index;
      studentState.is_submitted = attempt.is_submitted;
      studentState.submitted_test_session_id = attempt.submitted_test_session_id;
    })
    studentSubSessionRecords.forEach(entry => {
      let studentState = ensureStudentState(entry.uid)
      const sessionIndex = subSessionOrderIndex.get(entry.sub_session_id)
      let debugSubsessions = getPropVals(entry, [
        'is_submitted',
        'num_responses',
        'started_on',
        'last_locked_on',
        'last_touch_on',
        'subtracted_time',
      ]);
      studentState.subSessions[sessionIndex] = debugSubsessions;
    });

    return { studentStates, subSessionRecords };
  }
}
