import { ENFORCE_TIME_CONTROLS_NON_SAMPLE } from "../../../../../constants/site-flags";
import { Errors } from "../../../../../errors/general";
import { dbDateToMoment } from "../../../../../hooks/_util";
import { dateAsUtcDbString, dateEarliest, dateLatest, isDateFuture, isDatePast } from "../../../../../util/db-dates";
import { IAttemptTimeInfo } from "../model/types";

export interface IAttemptTimeEval {
    isDurationEnforced: boolean;
    started_on: string;
    ends_on: string;
    time_ext_m: number;
    duration_m: number;
    isNotStarted:boolean;
    isSessionEnded:boolean;
}

export class StudentSessionTimeControl {

    constructor(
        public attemptTimeInfo: IAttemptTimeInfo
    ) {}

    public evaluate(isDurationEnforced?:boolean){
        const info = this.attemptTimeInfo;
        console.log('API StudentSessionTimeControl.evaluate - attemptTimeInfo', JSON.stringify(info, null, 2));

        let isNotStarted = false;
        let isSessionEnded = false;
        const duration_m = info.ts_duration_m || info.twtar_duration_m || 0;
        const time_ext_m = (info.ta_time_ext_m || 0) + (info.ts_time_ext_m || 0)
        const started_on = info.ta_started_on || info.ts_started_on

        console.log('API StudentSessionTimeControl.evaluate - duration_m', duration_m);
        console.log('API StudentSessionTimeControl.evaluate - time_ext_m', time_ext_m);
        console.log('API StudentSessionTimeControl.evaluate - started_on', started_on);
        console.log('API StudentSessionTimeControl.evaluate - ta_started_on', info.ta_started_on);
        console.log('API StudentSessionTimeControl.evaluate - ts_started_on', info.ts_started_on);

        const momentSessionStart = dbDateToMoment(info.ts_started_on);
        const momentTimeStart = dbDateToMoment(info.ta_started_on || info.ts_started_on);
        const impliedTimeStart:any = dateLatest(momentSessionStart, momentTimeStart || momentSessionStart);

        console.log('API StudentSessionTimeControl.evaluate - momentSessionStart', momentSessionStart?.format());
        console.log('API StudentSessionTimeControl.evaluate - momentTimeStart', momentTimeStart?.format());
        console.log('API StudentSessionTimeControl.evaluate - impliedTimeStart', impliedTimeStart?.format());

        // Calculate total duration (like frontend should do)
        const totalDuration = duration_m + time_ext_m;
        console.log('API StudentSessionTimeControl.evaluate - totalDuration (duration_m + time_ext_m)', totalDuration);

        let momentTimeEnd;
        if (isDateFuture(momentSessionStart)){
            isNotStarted = true;
            console.log('API StudentSessionTimeControl.evaluate - Session not started yet');
        }
        let timeClose, timeHardStop;
        if (duration_m){
            timeClose = impliedTimeStart.clone().add(duration_m+time_ext_m, 'minutes');
            console.log('API StudentSessionTimeControl.evaluate - timeClose calculated', timeClose?.format());
            console.log('API StudentSessionTimeControl.evaluate - timeClose calculation: impliedTimeStart + (duration_m + time_ext_m) minutes');
            console.log('API StudentSessionTimeControl.evaluate - timeClose calculation:', impliedTimeStart?.format(), '+', (duration_m+time_ext_m), 'minutes =', timeClose?.format());
            if (isDatePast(timeClose.format())){
                isSessionEnded = true
                console.log('API StudentSessionTimeControl.evaluate - Session ended (duration)');
            }
        }
        if (info.hardstop_offset_h && info.twtar_is_sample==0){
            timeHardStop = impliedTimeStart.clone().add(info.hardstop_offset_h, 'hours');
            console.log('API StudentSessionTimeControl.evaluate - timeHardStop calculated', timeHardStop?.format());
            if (isDatePast(timeHardStop.format())){
                isSessionEnded = true
                console.log('API StudentSessionTimeControl.evaluate - Session ended (hardstop)');
            }
        }
        momentTimeEnd = dateEarliest(timeClose, timeHardStop);
        console.log('API StudentSessionTimeControl.evaluate - momentTimeEnd (earliest)', momentTimeEnd.format());

        if(info.twtar_test_date_end) {
            const momentTestEnd = dbDateToMoment(info.twtar_test_date_end);
            console.log('API StudentSessionTimeControl.evaluate - momentTestEnd', momentTestEnd?.format());
            momentTimeEnd = dateEarliest(momentTimeEnd, momentTestEnd);
            console.log('API StudentSessionTimeControl.evaluate - momentTimeEnd (after test end)', momentTimeEnd);
            if (isDatePast(momentTestEnd.format())){
                isSessionEnded = true
                console.log('API StudentSessionTimeControl.evaluate - Session ended (test window end)');
            }
        }

        const ends_on = dateAsUtcDbString(momentTimeEnd)
        console.log('API StudentSessionTimeControl.evaluate - ends_on', ends_on);

        // Calculate what the time remaining should be (like frontend does)
        const currentTime = new Date();
        const endTime = new Date(ends_on);
        const timeRemainingMs = endTime.getTime() - currentTime.getTime();
        const timeRemainingMinutes = Math.floor(timeRemainingMs / (1000 * 60));
        console.log('API StudentSessionTimeControl.evaluate - currentTime', currentTime.toISOString());
        console.log('API StudentSessionTimeControl.evaluate - endTime', endTime.toISOString());
        console.log('API StudentSessionTimeControl.evaluate - timeRemainingMs', timeRemainingMs);
        console.log('API StudentSessionTimeControl.evaluate - timeRemainingMinutes (what frontend should show)', timeRemainingMinutes);
        const timeSpent = 
        if (!isDurationEnforced){ // confirm if not passed in
            isDurationEnforced = this.checkIsEnforced();
        }
        console.log('API StudentSessionTimeControl.evaluate - isDurationEnforced', isDurationEnforced);
        const status:IAttemptTimeEval = {
            isDurationEnforced,
            started_on,
            ends_on,
            time_ext_m,
            duration_m,
            isNotStarted,
            isSessionEnded,
        }
        console.log('API StudentSessionTimeControl.evaluate - FINAL STATUS', JSON.stringify(status, null, 2));
        return status;
    }

    private checkIsEnforced(){
        const info = this.attemptTimeInfo;
        if (ENFORCE_TIME_CONTROLS_NON_SAMPLE){
            const twtar_is_sample = (info.twtar_is_sample == 0)
            const is_sample_time_enforced = (info.is_sample_time_enforced == 1)
            if (twtar_is_sample || is_sample_time_enforced || info.ts_duration_m){
                return true;
            }
        }
        return false
    }

    public validate(){
        let isEnforced = this.checkIsEnforced();
        if (isEnforced){
            const status = this.evaluate(isEnforced);
            if (status.isNotStarted){
                throw new Errors.Forbidden('ATTEMPT_PAUSED');
            }
            if (status.isSessionEnded){
                throw new Errors.Forbidden('SESSION_ENDED');
            }
        }
    }
}
